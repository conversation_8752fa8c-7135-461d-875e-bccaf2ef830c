-- Fix the supplier table constraint issue
-- This script will ensure the correct unique constraint exists

-- First, drop any existing constraints on id_external
ALTER TABLE supplier DROP CONSTRAINT IF EXISTS supplier_id_external_key;
ALTER TABLE supplier DROP CONSTRAINT IF EXISTS supplier_id_external_cooperative_key;

-- Add the correct unique constraint on the combination of id_external and id_cooperative
ALTER TABLE supplier ADD CONSTRAINT supplier_id_external_cooperative_key UNIQUE (id_external, id_cooperative);

-- Verify the constraint was created
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name,
    tc.constraint_type
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
WHERE 
    tc.table_name = 'supplier' 
    AND tc.constraint_type = 'UNIQUE'
ORDER BY tc.constraint_name, kcu.ordinal_position;
