package erpnext

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
)

//go:generate mockgen -source=Client.go -destination=mockClient.go -package=erpnext

type Client interface {
	GetUsers(instance Instance) ([]User, error)
	GetEmployees(instance Instance) ([]Employee, error)
	GetSuppliers(instance Instance) ([]Supplier, error)
	CreateSalesInvoice(instance Instance, invoice SalesInvoice) error
	GenerateSalesReport(instance Instance) (SalesReport, error)
	// GetPurchaseOrdersByDateRange(instance Instance, startDate, endDate time.Time) ([]ERPNextPurchaseOrder, error)
}

type ClientImpl struct {
	httpClient *http.Client
}

func NewClient() Client {
	return ClientImpl{
		httpClient: &http.Client{
			Timeout: time.Second * 120, // Increased timeout for large datasets
		},
	}
}

// NewClientWithTimeout creates a client with custom timeout
func NewClientWithTimeout(timeout time.Duration) Client {
	return ClientImpl{
		httpClient: &http.Client{
			Timeout: timeout,
		},
	}
}

// Implement methods for interacting with ERPNext API
// e.g., GetPayroll, CreateSale, GenerateReport, etc.

func (c ClientImpl) makeRequest(instance Instance, method, endpoint string, body interface{}) (*http.Response, error) {
	return c.makeRequestWithContext(context.Background(), instance, method, endpoint, body)
}

func (c ClientImpl) makeRequestWithContext(ctx context.Context, instance Instance, method, endpoint string, body interface{}) (*http.Response, error) {
	var buf bytes.Buffer
	if body != nil {
		if err := json.NewEncoder(&buf).Encode(body); err != nil {
			return nil, err
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, fmt.Sprintf("%s%s", instance.URL, endpoint), &buf)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", fmt.Sprintf("token %s", instance.APIKey))
	req.Header.Set("Content-Type", "application/json")

	return c.httpClient.Do(req)
}

// type DataResult[TR User | Supplier] struct {
// 	Data []TR `json:"data"`
// }

type DataResult[TR any] struct {
	Data []TR `json:"data"`
}

const (
	PAGE_LIMIT = 100
)

func paginatedGet[TR any](c ClientImpl, instance Instance, endpoint string, limitStart int) (DataResult[TR], error) {
	var result DataResult[TR]
	// paginatedEnpoint := fmt.Sprintf("%s&limit_start=%d", endpoint, limitStart)
	paginatedEnpoint := fmt.Sprintf("%s&limit=%d&limit_start=%d", endpoint, PAGE_LIMIT, limitStart)
	log.Println("Fetching data from", paginatedEnpoint)
	resp, err := c.makeRequest(instance, "GET", paginatedEnpoint, nil)
	if err != nil {
		return result, err
	}
	defer resp.Body.Close()

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return result, err
	}

	log.Println("Fetched", len(result.Data), "items")

	if len(result.Data) == PAGE_LIMIT {
		nextPage, err := paginatedGet[TR](c, instance, endpoint, limitStart+PAGE_LIMIT)
		if err != nil {
			return result, err
		}
		result.Data = append(result.Data, nextPage.Data...)
	}

	return result, nil
}

// Path: internal/erpnext/employee.go

type Employee struct {
	Name     string  `json:"name"`
	FullName string  `json:"employee_name"`
	Salary   float64 `json:"salary"`
}

func (c ClientImpl) GetEmployees(instance Instance) ([]Employee, error) {
	resp, err := c.makeRequest(instance, "GET", "/api/resource/Employee", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result struct {
		Data []Employee `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return result.Data, nil
}

type User struct {
	MobileNumber string `json:"mobile_no"`
	Email        string `json:"email"`

	UserType   string `json:"user_type"`
	UserName   string `json:"username"`
	ExternalID string `json:"name"`
}

func (c ClientImpl) GetUsers(instance Instance) ([]User, error) {
	result, err := paginatedGet[User](c, instance, "/api/resource/User?fields=[\"*\"]", 0)
	if err != nil {
		return nil, err
	}

	return result.Data, nil
}

type SalesInvoice struct {
	Customer   string  `json:"customer"`
	Items      []Item  `json:"items"`
	GrandTotal float64 `json:"grand_total"`
}

type Item struct {
	ItemCode string  `json:"item_code"`
	Qty      int     `json:"qty"`
	Rate     float64 `json:"rate"`
}

func (c ClientImpl) CreateSalesInvoice(instance Instance, invoice SalesInvoice) error {
	_, err := c.makeRequest(instance, "POST", "/api/resource/Sales Invoice", invoice)
	return err
}

// holder items import erpnet package and create a struct for sales report

type SalesReport struct {
	TotalSales float64 `json:"total_sales"`
	ItemsSold  int     `json:"items_sold"`
}

func (c ClientImpl) GenerateSalesReport(instance Instance) (SalesReport, error) {
	resp, err := c.makeRequest(instance, "GET", "/api/resource/Sales Invoice", nil)
	if err != nil {
		return SalesReport{}, err
	}
	defer resp.Body.Close()

	var result struct {
		Data []SalesInvoice `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return SalesReport{}, err
	}

	var totalSales float64
	var itemsSold int
	for _, invoice := range result.Data {
		totalSales += invoice.GrandTotal
		for _, item := range invoice.Items {
			itemsSold += item.Qty
		}
	}

	return SalesReport{
		TotalSales: totalSales,
		ItemsSold:  itemsSold,
	}, nil
}
