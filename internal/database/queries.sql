-- name: CreateUser :exec
INSERT INTO users (username, password) VALUES ($1, $2);

-- name: GetUserByUsername :one
SELECT * FROM users WHERE username = $1 LIMIT 1;

-- name: CreateCooperative :one
INSERT INTO cooperative (name, address, api_url, api_key) 
VALUES ($1, $2, $3, $4) 
RETURNING id;

-- name: ListCooperatives :many
SELECT * FROM cooperative;

-- name: ListCooperativesWithCounts :many
SELECT
    c.id,
    c.name,
    c.address,
    c.api_url,
    COALESCE(s.supplier_count, 0) as supplier_count,
    COALESCE(a.agent_count, 0) as agent_count
FROM cooperative c
LEFT JOIN (
    SELECT id_cooperative, COUNT(*) as supplier_count
    FROM supplier
    GROUP BY id_cooperative
) s ON c.id = s.id_cooperative
LEFT JOIN (
    SELECT id_cooperative, COUNT(*) as agent_count
    FROM user_cooperative
    GROUP BY id_cooperative
) a ON c.id = a.id_cooperative
ORDER BY c.name;

-- name: GetCooperative :one
SELECT * FROM cooperative WHERE id = $1;

-- name: UpdateCooperative :exec
UPDATE cooperative 
SET name = $2, address = $3, api_url = $4, api_key = $5
WHERE id = $1;

-- name: DeleteCooperative :exec
DELETE FROM cooperative WHERE id = $1;

-- name: CreateFarmer :one
INSERT INTO supplier (
    id_external,
    name, 
    sno, 
    mobile_number, 
    custom_id, 
    location, 
    bank, 
    bank_branch, 
    account_number, 
    gender,
    id_cooperative,
    id_type)

VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
RETURNING id;




-- name: ListFarmers :many
SELECT * FROM supplier WHERE id_cooperative = $1;

-- name: ListFarmersPaginated :many
SELECT * FROM supplier
WHERE id_cooperative = $1
ORDER BY name ASC
LIMIT $2 OFFSET $3;

-- name: CountFarmers :one
SELECT COUNT(*) FROM supplier WHERE id_cooperative = $1;

-- name: CountAgents :one
SELECT COUNT(*) FROM user_cooperative WHERE id_cooperative = $1;

-- name: CreateAgent :one
INSERT INTO app_user (email, mobile_number)
VALUES ($1, $2)
RETURNING id;

-- -- name: AssignAgentToCooperative :exec
-- INSERT INTO user_cooperative (external_id, id_user, id_cooperative, id_role, user_name)
-- VALUES ($1, $2, $3, $4, $5);



-- name: ListAgents :many
SELECT au.* FROM app_user au
JOIN user_cooperative uc ON au.id = uc.id_user
WHERE uc.id_cooperative = $1 AND uc.id_role = $2;

-- name: ListAllAgentsForCooperative :many
SELECT uc.id, au.email, au.mobile_number
FROM app_user au
JOIN user_cooperative uc ON au.id = uc.id_user
WHERE uc.id_cooperative = $1;


-- name: GetUserByEmail :one
SELECT id, email, mobile_number
FROM app_user
WHERE email = $1
LIMIT 1;


-- name: UpdateFarmer :exec
UPDATE supplier
SET name = $2, sno = $3, mobile_number = $4, custom_id = $5, location = $6, bank = $7, bank_branch = $8, account_number = $9, gender = $10, id_type = $11
WHERE id_external = $1 AND id_cooperative = $12;


-- name: GetFarmerByExternalID :one
SELECT * FROM supplier
WHERE id_external = $1 AND id_cooperative = $2;

-- name: DeleteFarmer :exec
DELETE FROM supplier WHERE id = $1;


-- name: SearchFarmers :many
SELECT * FROM supplier
WHERE (name LIKE $1 OR sno LIKE $2 OR mobile_number LIKE $3)
AND id_cooperative = $4;

-- name: SearchFarmersPaginated :many
SELECT * FROM supplier
WHERE (name LIKE $1 OR sno LIKE $2 OR mobile_number LIKE $3)
AND id_cooperative = $4
ORDER BY name ASC
LIMIT $5 OFFSET $6;

-- name: CountSearchFarmers :one
SELECT COUNT(*) FROM supplier
WHERE (name LIKE $1 OR sno LIKE $2 OR mobile_number LIKE $3)
AND id_cooperative = $4;

-- name: GetFarmer :one
SELECT * FROM supplier WHERE id = $1;

-- name: DeleteSuppliersByCoopID :exec
DELETE FROM supplier
WHERE id_cooperative = $1;


-- Updated InsertPurchaseOrder query
-- name: InsertPurchaseOrder :one
INSERT INTO purchase_orders (
    name, total_qty, custom_sno, contact_mobile, 
    custom_agent, status, supplier_name, 
    transaction_date, supplier_id, cooperative_id
) 
VALUES (
    $1, $2, $3, $4, 
    $5, $6, $7, 
    $8, $9, $10
)
RETURNING id;

-- New query to get purchase orders for a specific cooperative
-- name: GetPurchaseOrdersByCooperative :many
SELECT po.* 
FROM purchase_orders po
JOIN supplier s ON po.supplier_id = s.id
WHERE s.id_cooperative = $1;

-- Updated InsertMessage query
-- name: InsertMessage :one
INSERT INTO messages (error_code, error_description, data, purchase_order_id) 
VALUES ($1, $2, $3, $4)
RETURNING id;

-- New query to get messages for a specific purchase order
-- name: GetMessagesByPurchaseOrder :many
SELECT * FROM messages
WHERE purchase_order_id = $1
ORDER BY created_at DESC;

-- Get total quantity and amount for a supplier within a date range (unchanged)
-- name: GetSupplierTotalsByDateRange :one
SELECT 
    supplier_id,
    SUM(total_qty) AS total_quantity,
    SUM(total_qty * 1) AS total_amount
FROM 
    purchase_orders
WHERE 
    supplier_id = $1 AND
    transaction_date BETWEEN $2 AND $3
GROUP BY 
    supplier_id;

-- Get daily purchase order totals for all suppliers in a specific cooperative
-- name: GetDailyPurchaseOrderTotalsForCooperative :many
SELECT 
    po.supplier_id,
    po.transaction_date,
    SUM(po.total_qty) AS total_quantity,
    SUM(po.total_qty * 1) AS total_amount
FROM 
    purchase_orders po
JOIN supplier s ON po.supplier_id = s.id
WHERE 
    po.transaction_date = $1 AND
    s.id_cooperative = $2
GROUP BY 
    po.supplier_id, po.transaction_date;

-- Get monthly purchase order totals for a specific month, year, and cooperative
-- name: GetMonthlyPurchaseOrderTotalsForCooperative :many
SELECT 
    po.supplier_id,
    DATE_TRUNC('month', po.transaction_date) AS month,
    SUM(po.total_qty) AS total_quantity,
    SUM(po.total_qty * 1) AS total_amount
FROM 
    purchase_orders po
JOIN supplier s ON po.supplier_id = s.id
WHERE 
    EXTRACT(YEAR FROM po.transaction_date) = $1 AND
    EXTRACT(MONTH FROM po.transaction_date) = $2 AND
    s.id_cooperative = $3
GROUP BY 
    po.supplier_id, DATE_TRUNC('month', po.transaction_date);



-- name: CreatePurchaseOrder :one
INSERT INTO purchase_orders (
    name,
    total_qty,
    custom_sno,
    contact_mobile,
    custom_agent,
    status,
    supplier_name,
    transaction_date,
    total,
    cooperative_id,
    supplier_id,
    company,  -- New field
    creation  -- New field
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id, name, total_qty, custom_sno, contact_mobile, custom_agent, status, supplier_name, transaction_date, total, cooperative_id, supplier_id, created_at, company, creation;
-- name: GetPurchaseOrderById :one
SELECT
    id, name, total_qty, custom_sno, contact_mobile, custom_agent, status, supplier_name, transaction_date, total, cooperative_id, supplier_id, created_at
FROM
    purchase_orders
WHERE
    id = $1;

-- name: ListPurchaseOrders :many
SELECT
    id, name, total_qty, custom_sno, contact_mobile, custom_agent, status, supplier_name, transaction_date, total, cooperative_id, supplier_id, created_at
FROM
    purchase_orders
ORDER BY
    created_at DESC
LIMIT $1 OFFSET $2;





-- name: BatchUpdateFarmers :exec
WITH updates(id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_type) AS (
  SELECT unnest($1::text[]), unnest($2::text[]), unnest($3::text[]), unnest($4::text[]),
         unnest($5::text[]), unnest($6::text[]), unnest($7::text[]), unnest($8::text[]),
         unnest($9::text[]), unnest($10::text[]), unnest($11::int8[])
)
UPDATE supplier s
SET name = COALESCE(u.name, s.name),
    sno = COALESCE(u.sno, s.sno),
    mobile_number = COALESCE(u.mobile_number, s.mobile_number),
    custom_id = COALESCE(u.custom_id, s.custom_id),
    location = COALESCE(u.location, s.location),
    bank = COALESCE(u.bank, s.bank),
    bank_branch = COALESCE(u.bank_branch, s.bank_branch),
    account_number = COALESCE(u.account_number, s.account_number),
    gender = COALESCE(u.gender, s.gender),
    id_type = COALESCE(u.id_type, s.id_type)
FROM updates u
WHERE s.id_external = u.id_external
  AND s.id_cooperative = $12;


-- name: UpsertFarmer :exec
INSERT INTO supplier (
    id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_cooperative, id_type
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
)
ON CONFLICT (id_external) DO UPDATE
SET
    name = EXCLUDED.name,
    sno = EXCLUDED.sno,
    mobile_number = EXCLUDED.mobile_number,
    custom_id = EXCLUDED.custom_id,
    location = EXCLUDED.location,
    bank = EXCLUDED.bank,
    bank_branch = EXCLUDED.bank_branch,
    account_number = EXCLUDED.account_number,
    gender = EXCLUDED.gender,
    id_type = EXCLUDED.id_type
WHERE supplier.id_cooperative = EXCLUDED.id_cooperative;


-- name: ListFarmerExternalIDs :many
 SELECT id_external FROM supplier WHERE id_cooperative = $1;



-- name: GetUserByEmailOrMobile :one
SELECT * FROM app_user
WHERE email = $1 OR mobile_number = $2
LIMIT 1;


-- name: UpdateUser :exec
UPDATE app_user
SET email = $2, mobile_number = $3
WHERE id = $1;

-- name: IsUserAssignedToCooperative :one
SELECT EXISTS (
    SELECT 1 FROM user_cooperative
    WHERE id_user = $1 AND id_cooperative = $2
);


-- name: AssignAgentToCooperative :exec
INSERT INTO user_cooperative (user_name, external_id, id_user, id_cooperative, id_role)
VALUES ($1, $2, $3, $4, $5);


-- name: GetUserCooperativeAssignment :one
SELECT id_cooperative
FROM user_cooperative
WHERE id_user = $1
LIMIT 1;

-- Rates queries
-- name: CreateRate :one
INSERT INTO rates (month, base_rate, delivery_method, agent_id, cooperative_id, deduction)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING id;

-- name: ListRatesByCooperative :many
SELECT r.*, au.email as agent_email, au.mobile_number as agent_mobile
FROM rates r
LEFT JOIN user_cooperative uc ON r.agent_id = uc.id
LEFT JOIN app_user au ON uc.id_user = au.id
WHERE r.cooperative_id = $1
ORDER BY r.month DESC, au.email;

-- name: GetRate :one
SELECT r.*, au.email as agent_email, au.mobile_number as agent_mobile
FROM rates r
LEFT JOIN user_cooperative uc ON r.agent_id = uc.id
LEFT JOIN app_user au ON uc.id_user = au.id
WHERE r.id = $1;

-- name: UpdateRate :exec
UPDATE rates
SET month = $2, base_rate = $3, delivery_method = $4, agent_id = $5,
    cooperative_id = $6, deduction = $7
WHERE id = $1;

-- name: DeleteRate :exec
DELETE FROM rates WHERE id = $1;

-- name: GetRatesByCooperativeAndMonth :many
SELECT r.*, au.email as agent_email, au.mobile_number as agent_mobile
FROM rates r
LEFT JOIN user_cooperative uc ON r.agent_id = uc.id
LEFT JOIN app_user au ON uc.id_user = au.id
WHERE r.cooperative_id = $1 AND r.month = $2
ORDER BY au.email;