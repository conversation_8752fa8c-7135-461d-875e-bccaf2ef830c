// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: queries.sql

package database

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const assignAgentToCooperative = `-- name: AssignAgentToCooperative :exec
INSERT INTO user_cooperative (user_name, external_id, id_user, id_cooperative, id_role)
VALUES ($1, $2, $3, $4, $5)
`

type AssignAgentToCooperativeParams struct {
	UserName      string `json:"user_name"`
	ExternalID    string `json:"external_id"`
	IDUser        int64  `json:"id_user"`
	IDCooperative int64  `json:"id_cooperative"`
	IDRole        int64  `json:"id_role"`
}

func (q *Queries) AssignAgentToCooperative(ctx context.Context, arg AssignAgentToCooperativeParams) error {
	_, err := q.db.Exec(ctx, assignAgentToCooperative,
		arg.UserName,
		arg.ExternalID,
		arg.IDUser,
		arg.IDCooperative,
		arg.IDRole,
	)
	return err
}

const batchUpdateFarmers = `-- name: BatchUpdateFarmers :exec
WITH updates(id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_type) AS (
  SELECT unnest($1::text[]), unnest($2::text[]), unnest($3::text[]), unnest($4::text[]),
         unnest($5::text[]), unnest($6::text[]), unnest($7::text[]), unnest($8::text[]),
         unnest($9::text[]), unnest($10::text[]), unnest($11::int8[])
)
UPDATE supplier s
SET name = COALESCE(u.name, s.name),
    sno = COALESCE(u.sno, s.sno),
    mobile_number = COALESCE(u.mobile_number, s.mobile_number),
    custom_id = COALESCE(u.custom_id, s.custom_id),
    location = COALESCE(u.location, s.location),
    bank = COALESCE(u.bank, s.bank),
    bank_branch = COALESCE(u.bank_branch, s.bank_branch),
    account_number = COALESCE(u.account_number, s.account_number),
    gender = COALESCE(u.gender, s.gender),
    id_type = COALESCE(u.id_type, s.id_type)
FROM updates u
WHERE s.id_external = u.id_external
  AND s.id_cooperative = $12
`

type BatchUpdateFarmersParams struct {
	Column1       []string `json:"column_1"`
	Column2       []string `json:"column_2"`
	Column3       []string `json:"column_3"`
	Column4       []string `json:"column_4"`
	Column5       []string `json:"column_5"`
	Column6       []string `json:"column_6"`
	Column7       []string `json:"column_7"`
	Column8       []string `json:"column_8"`
	Column9       []string `json:"column_9"`
	Column10      []string `json:"column_10"`
	Column11      []int64  `json:"column_11"`
	IDCooperative int64    `json:"id_cooperative"`
}

func (q *Queries) BatchUpdateFarmers(ctx context.Context, arg BatchUpdateFarmersParams) error {
	_, err := q.db.Exec(ctx, batchUpdateFarmers,
		arg.Column1,
		arg.Column2,
		arg.Column3,
		arg.Column4,
		arg.Column5,
		arg.Column6,
		arg.Column7,
		arg.Column8,
		arg.Column9,
		arg.Column10,
		arg.Column11,
		arg.IDCooperative,
	)
	return err
}

const countFarmers = `-- name: CountFarmers :one
SELECT COUNT(*) FROM supplier WHERE id_cooperative = $1
`

func (q *Queries) CountFarmers(ctx context.Context, idCooperative int64) (int64, error) {
	row := q.db.QueryRow(ctx, countFarmers, idCooperative)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countSearchFarmers = `-- name: CountSearchFarmers :one
SELECT COUNT(*) FROM supplier
WHERE (name LIKE $1 OR sno LIKE $2 OR mobile_number LIKE $3)
AND id_cooperative = $4
`

type CountSearchFarmersParams struct {
	Name          string `json:"name"`
	Sno           string `json:"sno"`
	MobileNumber  string `json:"mobile_number"`
	IDCooperative int64  `json:"id_cooperative"`
}

func (q *Queries) CountSearchFarmers(ctx context.Context, arg CountSearchFarmersParams) (int64, error) {
	row := q.db.QueryRow(ctx, countSearchFarmers,
		arg.Name,
		arg.Sno,
		arg.MobileNumber,
		arg.IDCooperative,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createAgent = `-- name: CreateAgent :one
INSERT INTO app_user (email, mobile_number)
VALUES ($1, $2)
RETURNING id
`

type CreateAgentParams struct {
	Email        string `json:"email"`
	MobileNumber string `json:"mobile_number"`
}

func (q *Queries) CreateAgent(ctx context.Context, arg CreateAgentParams) (int32, error) {
	row := q.db.QueryRow(ctx, createAgent, arg.Email, arg.MobileNumber)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const createCooperative = `-- name: CreateCooperative :one
INSERT INTO cooperative (name, address, api_url, api_key) 
VALUES ($1, $2, $3, $4) 
RETURNING id
`

type CreateCooperativeParams struct {
	Name    string `json:"name"`
	Address string `json:"address"`
	ApiUrl  string `json:"api_url"`
	ApiKey  string `json:"api_key"`
}

func (q *Queries) CreateCooperative(ctx context.Context, arg CreateCooperativeParams) (int32, error) {
	row := q.db.QueryRow(ctx, createCooperative,
		arg.Name,
		arg.Address,
		arg.ApiUrl,
		arg.ApiKey,
	)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const createFarmer = `-- name: CreateFarmer :one
INSERT INTO supplier (
    id_external,
    name, 
    sno, 
    mobile_number, 
    custom_id, 
    location, 
    bank, 
    bank_branch, 
    account_number, 
    gender,
    id_cooperative,
    id_type)

VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
RETURNING id
`

type CreateFarmerParams struct {
	IDExternal    string  `json:"id_external"`
	Name          string  `json:"name"`
	Sno           string  `json:"sno"`
	MobileNumber  string  `json:"mobile_number"`
	CustomID      *string `json:"custom_id"`
	Location      *string `json:"location"`
	Bank          *string `json:"bank"`
	BankBranch    *string `json:"bank_branch"`
	AccountNumber *string `json:"account_number"`
	Gender        *string `json:"gender"`
	IDCooperative int64   `json:"id_cooperative"`
	IDType        int64   `json:"id_type"`
}

func (q *Queries) CreateFarmer(ctx context.Context, arg CreateFarmerParams) (int32, error) {
	row := q.db.QueryRow(ctx, createFarmer,
		arg.IDExternal,
		arg.Name,
		arg.Sno,
		arg.MobileNumber,
		arg.CustomID,
		arg.Location,
		arg.Bank,
		arg.BankBranch,
		arg.AccountNumber,
		arg.Gender,
		arg.IDCooperative,
		arg.IDType,
	)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const createPurchaseOrder = `-- name: CreatePurchaseOrder :one
INSERT INTO purchase_orders (
    name,
    total_qty,
    custom_sno,
    contact_mobile,
    custom_agent,
    status,
    supplier_name,
    transaction_date,
    total,
    cooperative_id,
    supplier_id,
    company,  -- New field
    creation  -- New field
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id, name, total_qty, custom_sno, contact_mobile, custom_agent, status, supplier_name, transaction_date, total, cooperative_id, supplier_id, created_at, company, creation
`

type CreatePurchaseOrderParams struct {
	Name            string             `json:"name"`
	TotalQty        pgtype.Numeric     `json:"total_qty"`
	CustomSno       *string            `json:"custom_sno"`
	ContactMobile   *string            `json:"contact_mobile"`
	CustomAgent     *string            `json:"custom_agent"`
	Status          *string            `json:"status"`
	SupplierName    *string            `json:"supplier_name"`
	TransactionDate pgtype.Date        `json:"transaction_date"`
	Total           pgtype.Numeric     `json:"total"`
	CooperativeID   *int32             `json:"cooperative_id"`
	SupplierID      *int32             `json:"supplier_id"`
	Company         *string            `json:"company"`
	Creation        pgtype.Timestamptz `json:"creation"`
}

func (q *Queries) CreatePurchaseOrder(ctx context.Context, arg CreatePurchaseOrderParams) (PurchaseOrder, error) {
	row := q.db.QueryRow(ctx, createPurchaseOrder,
		arg.Name,
		arg.TotalQty,
		arg.CustomSno,
		arg.ContactMobile,
		arg.CustomAgent,
		arg.Status,
		arg.SupplierName,
		arg.TransactionDate,
		arg.Total,
		arg.CooperativeID,
		arg.SupplierID,
		arg.Company,
		arg.Creation,
	)
	var i PurchaseOrder
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.TotalQty,
		&i.CustomSno,
		&i.ContactMobile,
		&i.CustomAgent,
		&i.Status,
		&i.SupplierName,
		&i.TransactionDate,
		&i.Total,
		&i.CooperativeID,
		&i.SupplierID,
		&i.CreatedAt,
		&i.Company,
		&i.Creation,
	)
	return i, err
}

const createRate = `-- name: CreateRate :one
INSERT INTO rates (month, base_rate, delivery_method, agent_id, cooperative_id, deduction)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING id
`

type CreateRateParams struct {
	Month          pgtype.Date    `json:"month"`
	BaseRate       pgtype.Numeric `json:"base_rate"`
	DeliveryMethod *string        `json:"delivery_method"`
	AgentID        *int32         `json:"agent_id"`
	CooperativeID  *int32         `json:"cooperative_id"`
	Deduction      pgtype.Numeric `json:"deduction"`
}

// Rates queries
func (q *Queries) CreateRate(ctx context.Context, arg CreateRateParams) (int32, error) {
	row := q.db.QueryRow(ctx, createRate,
		arg.Month,
		arg.BaseRate,
		arg.DeliveryMethod,
		arg.AgentID,
		arg.CooperativeID,
		arg.Deduction,
	)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const createUser = `-- name: CreateUser :exec
INSERT INTO users (username, password) VALUES ($1, $2)
`

type CreateUserParams struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) error {
	_, err := q.db.Exec(ctx, createUser, arg.Username, arg.Password)
	return err
}

const deleteCooperative = `-- name: DeleteCooperative :exec
DELETE FROM cooperative WHERE id = $1
`

func (q *Queries) DeleteCooperative(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteCooperative, id)
	return err
}

const deleteFarmer = `-- name: DeleteFarmer :exec
DELETE FROM supplier WHERE id = $1
`

func (q *Queries) DeleteFarmer(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteFarmer, id)
	return err
}

const deleteRate = `-- name: DeleteRate :exec
DELETE FROM rates WHERE id = $1
`

func (q *Queries) DeleteRate(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteRate, id)
	return err
}

const deleteSuppliersByCoopID = `-- name: DeleteSuppliersByCoopID :exec
DELETE FROM supplier
WHERE id_cooperative = $1
`

func (q *Queries) DeleteSuppliersByCoopID(ctx context.Context, idCooperative int64) error {
	_, err := q.db.Exec(ctx, deleteSuppliersByCoopID, idCooperative)
	return err
}

const getCooperative = `-- name: GetCooperative :one
SELECT id, name, address, api_url, api_key FROM cooperative WHERE id = $1
`

func (q *Queries) GetCooperative(ctx context.Context, id int32) (Cooperative, error) {
	row := q.db.QueryRow(ctx, getCooperative, id)
	var i Cooperative
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Address,
		&i.ApiUrl,
		&i.ApiKey,
	)
	return i, err
}

const getDailyPurchaseOrderTotalsForCooperative = `-- name: GetDailyPurchaseOrderTotalsForCooperative :many
SELECT 
    po.supplier_id,
    po.transaction_date,
    SUM(po.total_qty) AS total_quantity,
    SUM(po.total_qty * 1) AS total_amount
FROM 
    purchase_orders po
JOIN supplier s ON po.supplier_id = s.id
WHERE 
    po.transaction_date = $1 AND
    s.id_cooperative = $2
GROUP BY 
    po.supplier_id, po.transaction_date
`

type GetDailyPurchaseOrderTotalsForCooperativeParams struct {
	TransactionDate pgtype.Date `json:"transaction_date"`
	IDCooperative   int64       `json:"id_cooperative"`
}

type GetDailyPurchaseOrderTotalsForCooperativeRow struct {
	SupplierID      *int32      `json:"supplier_id"`
	TransactionDate pgtype.Date `json:"transaction_date"`
	TotalQuantity   int64       `json:"total_quantity"`
	TotalAmount     int64       `json:"total_amount"`
}

// Get daily purchase order totals for all suppliers in a specific cooperative
func (q *Queries) GetDailyPurchaseOrderTotalsForCooperative(ctx context.Context, arg GetDailyPurchaseOrderTotalsForCooperativeParams) ([]GetDailyPurchaseOrderTotalsForCooperativeRow, error) {
	rows, err := q.db.Query(ctx, getDailyPurchaseOrderTotalsForCooperative, arg.TransactionDate, arg.IDCooperative)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetDailyPurchaseOrderTotalsForCooperativeRow
	for rows.Next() {
		var i GetDailyPurchaseOrderTotalsForCooperativeRow
		if err := rows.Scan(
			&i.SupplierID,
			&i.TransactionDate,
			&i.TotalQuantity,
			&i.TotalAmount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getFarmer = `-- name: GetFarmer :one
SELECT id, id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_cooperative, id_type FROM supplier WHERE id = $1
`

func (q *Queries) GetFarmer(ctx context.Context, id int32) (Supplier, error) {
	row := q.db.QueryRow(ctx, getFarmer, id)
	var i Supplier
	err := row.Scan(
		&i.ID,
		&i.IDExternal,
		&i.Name,
		&i.Sno,
		&i.MobileNumber,
		&i.CustomID,
		&i.Location,
		&i.Bank,
		&i.BankBranch,
		&i.AccountNumber,
		&i.Gender,
		&i.IDCooperative,
		&i.IDType,
	)
	return i, err
}

const getFarmerByExternalID = `-- name: GetFarmerByExternalID :one
SELECT id, id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_cooperative, id_type FROM supplier
WHERE id_external = $1 AND id_cooperative = $2
`

type GetFarmerByExternalIDParams struct {
	IDExternal    string `json:"id_external"`
	IDCooperative int64  `json:"id_cooperative"`
}

func (q *Queries) GetFarmerByExternalID(ctx context.Context, arg GetFarmerByExternalIDParams) (Supplier, error) {
	row := q.db.QueryRow(ctx, getFarmerByExternalID, arg.IDExternal, arg.IDCooperative)
	var i Supplier
	err := row.Scan(
		&i.ID,
		&i.IDExternal,
		&i.Name,
		&i.Sno,
		&i.MobileNumber,
		&i.CustomID,
		&i.Location,
		&i.Bank,
		&i.BankBranch,
		&i.AccountNumber,
		&i.Gender,
		&i.IDCooperative,
		&i.IDType,
	)
	return i, err
}

const getMessagesByPurchaseOrder = `-- name: GetMessagesByPurchaseOrder :many
SELECT id, purchase_order_id, error_code, error_description, data, created_at FROM messages
WHERE purchase_order_id = $1
ORDER BY created_at DESC
`

// New query to get messages for a specific purchase order
func (q *Queries) GetMessagesByPurchaseOrder(ctx context.Context, purchaseOrderID *int32) ([]Message, error) {
	rows, err := q.db.Query(ctx, getMessagesByPurchaseOrder, purchaseOrderID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Message
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.PurchaseOrderID,
			&i.ErrorCode,
			&i.ErrorDescription,
			&i.Data,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getMonthlyPurchaseOrderTotalsForCooperative = `-- name: GetMonthlyPurchaseOrderTotalsForCooperative :many
SELECT 
    po.supplier_id,
    DATE_TRUNC('month', po.transaction_date) AS month,
    SUM(po.total_qty) AS total_quantity,
    SUM(po.total_qty * 1) AS total_amount
FROM 
    purchase_orders po
JOIN supplier s ON po.supplier_id = s.id
WHERE 
    EXTRACT(YEAR FROM po.transaction_date) = $1 AND
    EXTRACT(MONTH FROM po.transaction_date) = $2 AND
    s.id_cooperative = $3
GROUP BY 
    po.supplier_id, DATE_TRUNC('month', po.transaction_date)
`

type GetMonthlyPurchaseOrderTotalsForCooperativeParams struct {
	TransactionDate   pgtype.Date `json:"transaction_date"`
	TransactionDate_2 pgtype.Date `json:"transaction_date_2"`
	IDCooperative     int64       `json:"id_cooperative"`
}

type GetMonthlyPurchaseOrderTotalsForCooperativeRow struct {
	SupplierID    *int32          `json:"supplier_id"`
	Month         pgtype.Interval `json:"month"`
	TotalQuantity int64           `json:"total_quantity"`
	TotalAmount   int64           `json:"total_amount"`
}

// Get monthly purchase order totals for a specific month, year, and cooperative
func (q *Queries) GetMonthlyPurchaseOrderTotalsForCooperative(ctx context.Context, arg GetMonthlyPurchaseOrderTotalsForCooperativeParams) ([]GetMonthlyPurchaseOrderTotalsForCooperativeRow, error) {
	rows, err := q.db.Query(ctx, getMonthlyPurchaseOrderTotalsForCooperative, arg.TransactionDate, arg.TransactionDate_2, arg.IDCooperative)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetMonthlyPurchaseOrderTotalsForCooperativeRow
	for rows.Next() {
		var i GetMonthlyPurchaseOrderTotalsForCooperativeRow
		if err := rows.Scan(
			&i.SupplierID,
			&i.Month,
			&i.TotalQuantity,
			&i.TotalAmount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPurchaseOrderById = `-- name: GetPurchaseOrderById :one
SELECT
    id, name, total_qty, custom_sno, contact_mobile, custom_agent, status, supplier_name, transaction_date, total, cooperative_id, supplier_id, created_at
FROM
    purchase_orders
WHERE
    id = $1
`

type GetPurchaseOrderByIdRow struct {
	ID              int32              `json:"id"`
	Name            string             `json:"name"`
	TotalQty        pgtype.Numeric     `json:"total_qty"`
	CustomSno       *string            `json:"custom_sno"`
	ContactMobile   *string            `json:"contact_mobile"`
	CustomAgent     *string            `json:"custom_agent"`
	Status          *string            `json:"status"`
	SupplierName    *string            `json:"supplier_name"`
	TransactionDate pgtype.Date        `json:"transaction_date"`
	Total           pgtype.Numeric     `json:"total"`
	CooperativeID   *int32             `json:"cooperative_id"`
	SupplierID      *int32             `json:"supplier_id"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
}

func (q *Queries) GetPurchaseOrderById(ctx context.Context, id int32) (GetPurchaseOrderByIdRow, error) {
	row := q.db.QueryRow(ctx, getPurchaseOrderById, id)
	var i GetPurchaseOrderByIdRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.TotalQty,
		&i.CustomSno,
		&i.ContactMobile,
		&i.CustomAgent,
		&i.Status,
		&i.SupplierName,
		&i.TransactionDate,
		&i.Total,
		&i.CooperativeID,
		&i.SupplierID,
		&i.CreatedAt,
	)
	return i, err
}

const getPurchaseOrdersByCooperative = `-- name: GetPurchaseOrdersByCooperative :many
SELECT po.id, po.name, po.total_qty, po.custom_sno, po.contact_mobile, po.custom_agent, po.status, po.supplier_name, po.transaction_date, po.total, po.cooperative_id, po.supplier_id, po.created_at, po.company, po.creation 
FROM purchase_orders po
JOIN supplier s ON po.supplier_id = s.id
WHERE s.id_cooperative = $1
`

// New query to get purchase orders for a specific cooperative
func (q *Queries) GetPurchaseOrdersByCooperative(ctx context.Context, idCooperative int64) ([]PurchaseOrder, error) {
	rows, err := q.db.Query(ctx, getPurchaseOrdersByCooperative, idCooperative)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []PurchaseOrder
	for rows.Next() {
		var i PurchaseOrder
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.TotalQty,
			&i.CustomSno,
			&i.ContactMobile,
			&i.CustomAgent,
			&i.Status,
			&i.SupplierName,
			&i.TransactionDate,
			&i.Total,
			&i.CooperativeID,
			&i.SupplierID,
			&i.CreatedAt,
			&i.Company,
			&i.Creation,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRate = `-- name: GetRate :one
SELECT r.id, r.month, r.base_rate, r.delivery_method, r.agent_id, r.cooperative_id, r.deduction, r.final_rate, au.email as agent_email, au.mobile_number as agent_mobile
FROM rates r
LEFT JOIN user_cooperative uc ON r.agent_id = uc.id
LEFT JOIN app_user au ON uc.id_user = au.id
WHERE r.id = $1
`

type GetRateRow struct {
	ID             int32          `json:"id"`
	Month          pgtype.Date    `json:"month"`
	BaseRate       pgtype.Numeric `json:"base_rate"`
	DeliveryMethod *string        `json:"delivery_method"`
	AgentID        *int32         `json:"agent_id"`
	CooperativeID  *int32         `json:"cooperative_id"`
	Deduction      pgtype.Numeric `json:"deduction"`
	FinalRate      pgtype.Numeric `json:"final_rate"`
	AgentEmail     *string        `json:"agent_email"`
	AgentMobile    *string        `json:"agent_mobile"`
}

func (q *Queries) GetRate(ctx context.Context, id int32) (GetRateRow, error) {
	row := q.db.QueryRow(ctx, getRate, id)
	var i GetRateRow
	err := row.Scan(
		&i.ID,
		&i.Month,
		&i.BaseRate,
		&i.DeliveryMethod,
		&i.AgentID,
		&i.CooperativeID,
		&i.Deduction,
		&i.FinalRate,
		&i.AgentEmail,
		&i.AgentMobile,
	)
	return i, err
}

const getRatesByCooperativeAndMonth = `-- name: GetRatesByCooperativeAndMonth :many
SELECT r.id, r.month, r.base_rate, r.delivery_method, r.agent_id, r.cooperative_id, r.deduction, r.final_rate, au.email as agent_email, au.mobile_number as agent_mobile
FROM rates r
LEFT JOIN user_cooperative uc ON r.agent_id = uc.id
LEFT JOIN app_user au ON uc.id_user = au.id
WHERE r.cooperative_id = $1 AND r.month = $2
ORDER BY au.email
`

type GetRatesByCooperativeAndMonthParams struct {
	CooperativeID *int32      `json:"cooperative_id"`
	Month         pgtype.Date `json:"month"`
}

type GetRatesByCooperativeAndMonthRow struct {
	ID             int32          `json:"id"`
	Month          pgtype.Date    `json:"month"`
	BaseRate       pgtype.Numeric `json:"base_rate"`
	DeliveryMethod *string        `json:"delivery_method"`
	AgentID        *int32         `json:"agent_id"`
	CooperativeID  *int32         `json:"cooperative_id"`
	Deduction      pgtype.Numeric `json:"deduction"`
	FinalRate      pgtype.Numeric `json:"final_rate"`
	AgentEmail     *string        `json:"agent_email"`
	AgentMobile    *string        `json:"agent_mobile"`
}

func (q *Queries) GetRatesByCooperativeAndMonth(ctx context.Context, arg GetRatesByCooperativeAndMonthParams) ([]GetRatesByCooperativeAndMonthRow, error) {
	rows, err := q.db.Query(ctx, getRatesByCooperativeAndMonth, arg.CooperativeID, arg.Month)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetRatesByCooperativeAndMonthRow
	for rows.Next() {
		var i GetRatesByCooperativeAndMonthRow
		if err := rows.Scan(
			&i.ID,
			&i.Month,
			&i.BaseRate,
			&i.DeliveryMethod,
			&i.AgentID,
			&i.CooperativeID,
			&i.Deduction,
			&i.FinalRate,
			&i.AgentEmail,
			&i.AgentMobile,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSupplierTotalsByDateRange = `-- name: GetSupplierTotalsByDateRange :one
SELECT 
    supplier_id,
    SUM(total_qty) AS total_quantity,
    SUM(total_qty * 1) AS total_amount
FROM 
    purchase_orders
WHERE 
    supplier_id = $1 AND
    transaction_date BETWEEN $2 AND $3
GROUP BY 
    supplier_id
`

type GetSupplierTotalsByDateRangeParams struct {
	SupplierID        *int32      `json:"supplier_id"`
	TransactionDate   pgtype.Date `json:"transaction_date"`
	TransactionDate_2 pgtype.Date `json:"transaction_date_2"`
}

type GetSupplierTotalsByDateRangeRow struct {
	SupplierID    *int32 `json:"supplier_id"`
	TotalQuantity int64  `json:"total_quantity"`
	TotalAmount   int64  `json:"total_amount"`
}

// Get total quantity and amount for a supplier within a date range (unchanged)
func (q *Queries) GetSupplierTotalsByDateRange(ctx context.Context, arg GetSupplierTotalsByDateRangeParams) (GetSupplierTotalsByDateRangeRow, error) {
	row := q.db.QueryRow(ctx, getSupplierTotalsByDateRange, arg.SupplierID, arg.TransactionDate, arg.TransactionDate_2)
	var i GetSupplierTotalsByDateRangeRow
	err := row.Scan(&i.SupplierID, &i.TotalQuantity, &i.TotalAmount)
	return i, err
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id, email, mobile_number
FROM app_user
WHERE email = $1
LIMIT 1
`

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (AppUser, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i AppUser
	err := row.Scan(&i.ID, &i.Email, &i.MobileNumber)
	return i, err
}

const getUserByEmailOrMobile = `-- name: GetUserByEmailOrMobile :one
SELECT id, email, mobile_number FROM app_user
WHERE email = $1 OR mobile_number = $2
LIMIT 1
`

type GetUserByEmailOrMobileParams struct {
	Email        string `json:"email"`
	MobileNumber string `json:"mobile_number"`
}

func (q *Queries) GetUserByEmailOrMobile(ctx context.Context, arg GetUserByEmailOrMobileParams) (AppUser, error) {
	row := q.db.QueryRow(ctx, getUserByEmailOrMobile, arg.Email, arg.MobileNumber)
	var i AppUser
	err := row.Scan(&i.ID, &i.Email, &i.MobileNumber)
	return i, err
}

const getUserByUsername = `-- name: GetUserByUsername :one
SELECT id, username, password FROM users WHERE username = $1 LIMIT 1
`

func (q *Queries) GetUserByUsername(ctx context.Context, username string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByUsername, username)
	var i User
	err := row.Scan(&i.ID, &i.Username, &i.Password)
	return i, err
}

const getUserCooperativeAssignment = `-- name: GetUserCooperativeAssignment :one
SELECT id_cooperative
FROM user_cooperative
WHERE id_user = $1
LIMIT 1
`

func (q *Queries) GetUserCooperativeAssignment(ctx context.Context, idUser int64) (int64, error) {
	row := q.db.QueryRow(ctx, getUserCooperativeAssignment, idUser)
	var id_cooperative int64
	err := row.Scan(&id_cooperative)
	return id_cooperative, err
}

const insertMessage = `-- name: InsertMessage :one
INSERT INTO messages (error_code, error_description, data, purchase_order_id) 
VALUES ($1, $2, $3, $4)
RETURNING id
`

type InsertMessageParams struct {
	ErrorCode        *int32  `json:"error_code"`
	ErrorDescription *string `json:"error_description"`
	Data             []byte  `json:"data"`
	PurchaseOrderID  *int32  `json:"purchase_order_id"`
}

// Updated InsertMessage query
func (q *Queries) InsertMessage(ctx context.Context, arg InsertMessageParams) (int32, error) {
	row := q.db.QueryRow(ctx, insertMessage,
		arg.ErrorCode,
		arg.ErrorDescription,
		arg.Data,
		arg.PurchaseOrderID,
	)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const insertPurchaseOrder = `-- name: InsertPurchaseOrder :one
INSERT INTO purchase_orders (
    name, total_qty, custom_sno, contact_mobile, 
    custom_agent, status, supplier_name, 
    transaction_date, supplier_id, cooperative_id
) 
VALUES (
    $1, $2, $3, $4, 
    $5, $6, $7, 
    $8, $9, $10
)
RETURNING id
`

type InsertPurchaseOrderParams struct {
	Name            string         `json:"name"`
	TotalQty        pgtype.Numeric `json:"total_qty"`
	CustomSno       *string        `json:"custom_sno"`
	ContactMobile   *string        `json:"contact_mobile"`
	CustomAgent     *string        `json:"custom_agent"`
	Status          *string        `json:"status"`
	SupplierName    *string        `json:"supplier_name"`
	TransactionDate pgtype.Date    `json:"transaction_date"`
	SupplierID      *int32         `json:"supplier_id"`
	CooperativeID   *int32         `json:"cooperative_id"`
}

// Updated InsertPurchaseOrder query
func (q *Queries) InsertPurchaseOrder(ctx context.Context, arg InsertPurchaseOrderParams) (int32, error) {
	row := q.db.QueryRow(ctx, insertPurchaseOrder,
		arg.Name,
		arg.TotalQty,
		arg.CustomSno,
		arg.ContactMobile,
		arg.CustomAgent,
		arg.Status,
		arg.SupplierName,
		arg.TransactionDate,
		arg.SupplierID,
		arg.CooperativeID,
	)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const isUserAssignedToCooperative = `-- name: IsUserAssignedToCooperative :one
SELECT EXISTS (
    SELECT 1 FROM user_cooperative
    WHERE id_user = $1 AND id_cooperative = $2
)
`

type IsUserAssignedToCooperativeParams struct {
	IDUser        int64 `json:"id_user"`
	IDCooperative int64 `json:"id_cooperative"`
}

func (q *Queries) IsUserAssignedToCooperative(ctx context.Context, arg IsUserAssignedToCooperativeParams) (bool, error) {
	row := q.db.QueryRow(ctx, isUserAssignedToCooperative, arg.IDUser, arg.IDCooperative)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const listAgents = `-- name: ListAgents :many



SELECT au.id, au.email, au.mobile_number FROM app_user au
JOIN user_cooperative uc ON au.id = uc.id_user
WHERE uc.id_cooperative = $1 AND uc.id_role = $2
`

type ListAgentsParams struct {
	IDCooperative int64 `json:"id_cooperative"`
	IDRole        int64 `json:"id_role"`
}

// -- name: AssignAgentToCooperative :exec
// INSERT INTO user_cooperative (external_id, id_user, id_cooperative, id_role, user_name)
// VALUES ($1, $2, $3, $4, $5);
func (q *Queries) ListAgents(ctx context.Context, arg ListAgentsParams) ([]AppUser, error) {
	rows, err := q.db.Query(ctx, listAgents, arg.IDCooperative, arg.IDRole)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []AppUser
	for rows.Next() {
		var i AppUser
		if err := rows.Scan(&i.ID, &i.Email, &i.MobileNumber); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAllAgentsForCooperative = `-- name: ListAllAgentsForCooperative :many
SELECT uc.id, au.email, au.mobile_number
FROM app_user au
JOIN user_cooperative uc ON au.id = uc.id_user
WHERE uc.id_cooperative = $1
`

type ListAllAgentsForCooperativeRow struct {
	ID           int32  `json:"id"`
	Email        string `json:"email"`
	MobileNumber string `json:"mobile_number"`
}

func (q *Queries) ListAllAgentsForCooperative(ctx context.Context, idCooperative int64) ([]ListAllAgentsForCooperativeRow, error) {
	rows, err := q.db.Query(ctx, listAllAgentsForCooperative, idCooperative)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListAllAgentsForCooperativeRow
	for rows.Next() {
		var i ListAllAgentsForCooperativeRow
		if err := rows.Scan(&i.ID, &i.Email, &i.MobileNumber); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listCooperatives = `-- name: ListCooperatives :many
SELECT id, name, address, api_url, api_key FROM cooperative
`

func (q *Queries) ListCooperatives(ctx context.Context) ([]Cooperative, error) {
	rows, err := q.db.Query(ctx, listCooperatives)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Cooperative
	for rows.Next() {
		var i Cooperative
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Address,
			&i.ApiUrl,
			&i.ApiKey,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listFarmerExternalIDs = `-- name: ListFarmerExternalIDs :many
 SELECT id_external FROM supplier WHERE id_cooperative = $1
`

func (q *Queries) ListFarmerExternalIDs(ctx context.Context, idCooperative int64) ([]string, error) {
	rows, err := q.db.Query(ctx, listFarmerExternalIDs, idCooperative)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []string
	for rows.Next() {
		var id_external string
		if err := rows.Scan(&id_external); err != nil {
			return nil, err
		}
		items = append(items, id_external)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listFarmers = `-- name: ListFarmers :many
SELECT id, id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_cooperative, id_type FROM supplier WHERE id_cooperative = $1
`

func (q *Queries) ListFarmers(ctx context.Context, idCooperative int64) ([]Supplier, error) {
	rows, err := q.db.Query(ctx, listFarmers, idCooperative)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Supplier
	for rows.Next() {
		var i Supplier
		if err := rows.Scan(
			&i.ID,
			&i.IDExternal,
			&i.Name,
			&i.Sno,
			&i.MobileNumber,
			&i.CustomID,
			&i.Location,
			&i.Bank,
			&i.BankBranch,
			&i.AccountNumber,
			&i.Gender,
			&i.IDCooperative,
			&i.IDType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listFarmersPaginated = `-- name: ListFarmersPaginated :many
SELECT id, id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_cooperative, id_type FROM supplier
WHERE id_cooperative = $1
ORDER BY name ASC
LIMIT $2 OFFSET $3
`

type ListFarmersPaginatedParams struct {
	IDCooperative int64 `json:"id_cooperative"`
	Limit         int32 `json:"limit"`
	Offset        int32 `json:"offset"`
}

func (q *Queries) ListFarmersPaginated(ctx context.Context, arg ListFarmersPaginatedParams) ([]Supplier, error) {
	rows, err := q.db.Query(ctx, listFarmersPaginated, arg.IDCooperative, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Supplier
	for rows.Next() {
		var i Supplier
		if err := rows.Scan(
			&i.ID,
			&i.IDExternal,
			&i.Name,
			&i.Sno,
			&i.MobileNumber,
			&i.CustomID,
			&i.Location,
			&i.Bank,
			&i.BankBranch,
			&i.AccountNumber,
			&i.Gender,
			&i.IDCooperative,
			&i.IDType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPurchaseOrders = `-- name: ListPurchaseOrders :many
SELECT
    id, name, total_qty, custom_sno, contact_mobile, custom_agent, status, supplier_name, transaction_date, total, cooperative_id, supplier_id, created_at
FROM
    purchase_orders
ORDER BY
    created_at DESC
LIMIT $1 OFFSET $2
`

type ListPurchaseOrdersParams struct {
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

type ListPurchaseOrdersRow struct {
	ID              int32              `json:"id"`
	Name            string             `json:"name"`
	TotalQty        pgtype.Numeric     `json:"total_qty"`
	CustomSno       *string            `json:"custom_sno"`
	ContactMobile   *string            `json:"contact_mobile"`
	CustomAgent     *string            `json:"custom_agent"`
	Status          *string            `json:"status"`
	SupplierName    *string            `json:"supplier_name"`
	TransactionDate pgtype.Date        `json:"transaction_date"`
	Total           pgtype.Numeric     `json:"total"`
	CooperativeID   *int32             `json:"cooperative_id"`
	SupplierID      *int32             `json:"supplier_id"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
}

func (q *Queries) ListPurchaseOrders(ctx context.Context, arg ListPurchaseOrdersParams) ([]ListPurchaseOrdersRow, error) {
	rows, err := q.db.Query(ctx, listPurchaseOrders, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListPurchaseOrdersRow
	for rows.Next() {
		var i ListPurchaseOrdersRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.TotalQty,
			&i.CustomSno,
			&i.ContactMobile,
			&i.CustomAgent,
			&i.Status,
			&i.SupplierName,
			&i.TransactionDate,
			&i.Total,
			&i.CooperativeID,
			&i.SupplierID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listRatesByCooperative = `-- name: ListRatesByCooperative :many
SELECT r.id, r.month, r.base_rate, r.delivery_method, r.agent_id, r.cooperative_id, r.deduction, r.final_rate, au.email as agent_email, au.mobile_number as agent_mobile
FROM rates r
LEFT JOIN user_cooperative uc ON r.agent_id = uc.id
LEFT JOIN app_user au ON uc.id_user = au.id
WHERE r.cooperative_id = $1
ORDER BY r.month DESC, au.email
`

type ListRatesByCooperativeRow struct {
	ID             int32          `json:"id"`
	Month          pgtype.Date    `json:"month"`
	BaseRate       pgtype.Numeric `json:"base_rate"`
	DeliveryMethod *string        `json:"delivery_method"`
	AgentID        *int32         `json:"agent_id"`
	CooperativeID  *int32         `json:"cooperative_id"`
	Deduction      pgtype.Numeric `json:"deduction"`
	FinalRate      pgtype.Numeric `json:"final_rate"`
	AgentEmail     *string        `json:"agent_email"`
	AgentMobile    *string        `json:"agent_mobile"`
}

func (q *Queries) ListRatesByCooperative(ctx context.Context, cooperativeID *int32) ([]ListRatesByCooperativeRow, error) {
	rows, err := q.db.Query(ctx, listRatesByCooperative, cooperativeID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListRatesByCooperativeRow
	for rows.Next() {
		var i ListRatesByCooperativeRow
		if err := rows.Scan(
			&i.ID,
			&i.Month,
			&i.BaseRate,
			&i.DeliveryMethod,
			&i.AgentID,
			&i.CooperativeID,
			&i.Deduction,
			&i.FinalRate,
			&i.AgentEmail,
			&i.AgentMobile,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchFarmers = `-- name: SearchFarmers :many
SELECT id, id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_cooperative, id_type FROM supplier
WHERE (name LIKE $1 OR sno LIKE $2 OR mobile_number LIKE $3)
AND id_cooperative = $4
`

type SearchFarmersParams struct {
	Name          string `json:"name"`
	Sno           string `json:"sno"`
	MobileNumber  string `json:"mobile_number"`
	IDCooperative int64  `json:"id_cooperative"`
}

func (q *Queries) SearchFarmers(ctx context.Context, arg SearchFarmersParams) ([]Supplier, error) {
	rows, err := q.db.Query(ctx, searchFarmers,
		arg.Name,
		arg.Sno,
		arg.MobileNumber,
		arg.IDCooperative,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Supplier
	for rows.Next() {
		var i Supplier
		if err := rows.Scan(
			&i.ID,
			&i.IDExternal,
			&i.Name,
			&i.Sno,
			&i.MobileNumber,
			&i.CustomID,
			&i.Location,
			&i.Bank,
			&i.BankBranch,
			&i.AccountNumber,
			&i.Gender,
			&i.IDCooperative,
			&i.IDType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchFarmersPaginated = `-- name: SearchFarmersPaginated :many
SELECT id, id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_cooperative, id_type FROM supplier
WHERE (name LIKE $1 OR sno LIKE $2 OR mobile_number LIKE $3)
AND id_cooperative = $4
ORDER BY name ASC
LIMIT $5 OFFSET $6
`

type SearchFarmersPaginatedParams struct {
	Name          string `json:"name"`
	Sno           string `json:"sno"`
	MobileNumber  string `json:"mobile_number"`
	IDCooperative int64  `json:"id_cooperative"`
	Limit         int32  `json:"limit"`
	Offset        int32  `json:"offset"`
}

func (q *Queries) SearchFarmersPaginated(ctx context.Context, arg SearchFarmersPaginatedParams) ([]Supplier, error) {
	rows, err := q.db.Query(ctx, searchFarmersPaginated,
		arg.Name,
		arg.Sno,
		arg.MobileNumber,
		arg.IDCooperative,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Supplier
	for rows.Next() {
		var i Supplier
		if err := rows.Scan(
			&i.ID,
			&i.IDExternal,
			&i.Name,
			&i.Sno,
			&i.MobileNumber,
			&i.CustomID,
			&i.Location,
			&i.Bank,
			&i.BankBranch,
			&i.AccountNumber,
			&i.Gender,
			&i.IDCooperative,
			&i.IDType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateCooperative = `-- name: UpdateCooperative :exec
UPDATE cooperative 
SET name = $2, address = $3, api_url = $4, api_key = $5
WHERE id = $1
`

type UpdateCooperativeParams struct {
	ID      int32  `json:"id"`
	Name    string `json:"name"`
	Address string `json:"address"`
	ApiUrl  string `json:"api_url"`
	ApiKey  string `json:"api_key"`
}

func (q *Queries) UpdateCooperative(ctx context.Context, arg UpdateCooperativeParams) error {
	_, err := q.db.Exec(ctx, updateCooperative,
		arg.ID,
		arg.Name,
		arg.Address,
		arg.ApiUrl,
		arg.ApiKey,
	)
	return err
}

const updateFarmer = `-- name: UpdateFarmer :exec
UPDATE supplier
SET name = $2, sno = $3, mobile_number = $4, custom_id = $5, location = $6, bank = $7, bank_branch = $8, account_number = $9, gender = $10, id_type = $11
WHERE id_external = $1 AND id_cooperative = $12
`

type UpdateFarmerParams struct {
	IDExternal    string  `json:"id_external"`
	Name          string  `json:"name"`
	Sno           string  `json:"sno"`
	MobileNumber  string  `json:"mobile_number"`
	CustomID      *string `json:"custom_id"`
	Location      *string `json:"location"`
	Bank          *string `json:"bank"`
	BankBranch    *string `json:"bank_branch"`
	AccountNumber *string `json:"account_number"`
	Gender        *string `json:"gender"`
	IDType        int64   `json:"id_type"`
	IDCooperative int64   `json:"id_cooperative"`
}

func (q *Queries) UpdateFarmer(ctx context.Context, arg UpdateFarmerParams) error {
	_, err := q.db.Exec(ctx, updateFarmer,
		arg.IDExternal,
		arg.Name,
		arg.Sno,
		arg.MobileNumber,
		arg.CustomID,
		arg.Location,
		arg.Bank,
		arg.BankBranch,
		arg.AccountNumber,
		arg.Gender,
		arg.IDType,
		arg.IDCooperative,
	)
	return err
}

const updateRate = `-- name: UpdateRate :exec
UPDATE rates
SET month = $2, base_rate = $3, delivery_method = $4, agent_id = $5,
    cooperative_id = $6, deduction = $7
WHERE id = $1
`

type UpdateRateParams struct {
	ID             int32          `json:"id"`
	Month          pgtype.Date    `json:"month"`
	BaseRate       pgtype.Numeric `json:"base_rate"`
	DeliveryMethod *string        `json:"delivery_method"`
	AgentID        *int32         `json:"agent_id"`
	CooperativeID  *int32         `json:"cooperative_id"`
	Deduction      pgtype.Numeric `json:"deduction"`
}

func (q *Queries) UpdateRate(ctx context.Context, arg UpdateRateParams) error {
	_, err := q.db.Exec(ctx, updateRate,
		arg.ID,
		arg.Month,
		arg.BaseRate,
		arg.DeliveryMethod,
		arg.AgentID,
		arg.CooperativeID,
		arg.Deduction,
	)
	return err
}

const updateUser = `-- name: UpdateUser :exec
UPDATE app_user
SET email = $2, mobile_number = $3
WHERE id = $1
`

type UpdateUserParams struct {
	ID           int32  `json:"id"`
	Email        string `json:"email"`
	MobileNumber string `json:"mobile_number"`
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) error {
	_, err := q.db.Exec(ctx, updateUser, arg.ID, arg.Email, arg.MobileNumber)
	return err
}

const upsertFarmer = `-- name: UpsertFarmer :exec
INSERT INTO supplier (
    id_external, name, sno, mobile_number, custom_id, location, bank, bank_branch, account_number, gender, id_cooperative, id_type
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
)
ON CONFLICT (id_external) DO UPDATE
SET
    name = EXCLUDED.name,
    sno = EXCLUDED.sno,
    mobile_number = EXCLUDED.mobile_number,
    custom_id = EXCLUDED.custom_id,
    location = EXCLUDED.location,
    bank = EXCLUDED.bank,
    bank_branch = EXCLUDED.bank_branch,
    account_number = EXCLUDED.account_number,
    gender = EXCLUDED.gender,
    id_type = EXCLUDED.id_type
WHERE supplier.id_cooperative = EXCLUDED.id_cooperative
`

type UpsertFarmerParams struct {
	IDExternal    string  `json:"id_external"`
	Name          string  `json:"name"`
	Sno           string  `json:"sno"`
	MobileNumber  string  `json:"mobile_number"`
	CustomID      *string `json:"custom_id"`
	Location      *string `json:"location"`
	Bank          *string `json:"bank"`
	BankBranch    *string `json:"bank_branch"`
	AccountNumber *string `json:"account_number"`
	Gender        *string `json:"gender"`
	IDCooperative int64   `json:"id_cooperative"`
	IDType        int64   `json:"id_type"`
}

func (q *Queries) UpsertFarmer(ctx context.Context, arg UpsertFarmerParams) error {
	_, err := q.db.Exec(ctx, upsertFarmer,
		arg.IDExternal,
		arg.Name,
		arg.Sno,
		arg.MobileNumber,
		arg.CustomID,
		arg.Location,
		arg.Bank,
		arg.BankBranch,
		arg.AccountNumber,
		arg.Gender,
		arg.IDCooperative,
		arg.IDType,
	)
	return err
}
