package middleware

import (
	"context"
	"log"
	"net/http"
	"strings"

	"github.com/itunza/africascongress/internal/auth"
)

func AuthMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var token string
		isAPIRequest := strings.HasPrefix(r.URL.Path, "/api")

		// Check for Authorization header
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			// Split the header value into "Bearer" and the token
			tokenParts := strings.Split(authHeader, " ")
			if len(tokenParts) == 2 && tokenParts[0] == "Bearer" {
				token = tokenParts[1]
			} else {
				if isAPIRequest {
					http.Error(w, "Invalid Authorization header format", http.StatusUnauthorized)
				} else {
					http.Redirect(w, r, "/login", http.StatusSeeOther)
				}
				return
			}
		} else {
			// If no Authorization header, check for auth_token cookie
			cookie, err := r.<PERSON>("auth_token")
			if err != nil {
				if isAPIRequest {
					http.Error(w, "Authorization required", http.StatusUnauthorized)
				} else {
					http.Redirect(w, r, "/login", http.StatusSeeOther)
				}
				return
			}
			token = cookie.Value
		}

		// Validate the token
		claims, err := auth.ValidateToken(token)
		if err != nil {
			log.Printf("Token validation error: %v", err)
			if isAPIRequest {
				http.Error(w, "Invalid or expired token", http.StatusUnauthorized)
			} else {
				http.SetCookie(w, &http.Cookie{
					Name:   "auth_token",
					Value:  "",
					MaxAge: -1,
				})
				http.Redirect(w, r, "/login", http.StatusSeeOther)
			}
			return
		}

		// Add user information to the context
		ctx := context.WithValue(r.Context(), "username", claims.Username)
		next.ServeHTTP(w, r.WithContext(ctx))
	}
}

// PageAuthMiddleware is specifically for protecting web pages (non-API routes)
// It always redirects to login page on authentication failure
func PageAuthMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var token string

		// Check for Authorization header first
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			tokenParts := strings.Split(authHeader, " ")
			if len(tokenParts) == 2 && tokenParts[0] == "Bearer" {
				token = tokenParts[1]
			} else {
				http.Redirect(w, r, "/login", http.StatusSeeOther)
				return
			}
		} else {
			// Check for auth_token cookie
			cookie, err := r.Cookie("auth_token")
			if err != nil {
				http.Redirect(w, r, "/login", http.StatusSeeOther)
				return
			}
			token = cookie.Value
		}

		// Validate the token
		claims, err := auth.ValidateToken(token)
		if err != nil {
			log.Printf("Token validation error: %v", err)
			// Clear invalid cookie
			http.SetCookie(w, &http.Cookie{
				Name:   "auth_token",
				Value:  "",
				MaxAge: -1,
				Path:   "/",
			})
			http.Redirect(w, r, "/login", http.StatusSeeOther)
			return
		}

		// Add user information to the context
		ctx := context.WithValue(r.Context(), "username", claims.Username)
		next.ServeHTTP(w, r.WithContext(ctx))
	}
}
