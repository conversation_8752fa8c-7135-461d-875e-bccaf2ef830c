package services

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/internal/enums"
	"github.com/itunza/africascongress/internal/erpnext"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

// ImprovedAgentSyncService provides enhanced agent sync operations with progress tracking
type ImprovedAgentSyncService struct {
	db        *database.Queries
	erpClient erpnext.Client
	config    SyncConfig
	progress  map[string]*SyncProgress
	mu        sync.RWMutex
}

// NewImprovedAgentSyncService creates a new improved agent sync service
func NewImprovedAgentSyncService(db *database.Queries, erpClient erpnext.Client, config SyncConfig) *ImprovedAgentSyncService {
	return &ImprovedAgentSyncService{
		db:        db,
		erpClient: erpClient,
		config:    config,
		progress:  make(map[string]*SyncProgress),
	}
}

// SyncAgentsWithProgress performs agent sync with progress tracking
func (s *ImprovedAgentSyncService) SyncAgentsWithProgress(ctx context.Context, cooperativeID int, syncID string) (*SyncResult, error) {
	startTime := time.Now()

	// Initialize progress
	s.mu.Lock()
	s.progress[syncID] = &SyncProgress{
		Status:    "starting",
		Message:   "Initializing agent sync...",
		StartTime: startTime,
	}
	s.mu.Unlock()

	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, s.config.Timeout)
	defer cancel()

	result := &SyncResult{
		Success: false,
	}

	// Get cooperative details
	s.updateProgress(syncID, func(p *SyncProgress) {
		p.Status = "fetching_cooperative"
		p.Message = "Fetching cooperative details..."
	})

	log.Printf("Attempting to get cooperative with ID: %d", cooperativeID)
	cooperative, err := s.db.GetCooperative(ctx, int32(cooperativeID))
	if err != nil {
		log.Printf("Failed to get cooperative %d: %v", cooperativeID, err)
		s.updateProgress(syncID, func(p *SyncProgress) {
			p.Status = "error"
			p.Message = fmt.Sprintf("Failed to get cooperative: %v", err)
		})
		return result, fmt.Errorf("failed to get cooperative: %w", err)
	}
	log.Printf("Successfully retrieved cooperative: %s (URL: %s)", cooperative.Name, cooperative.ApiUrl)

	// Fetch users from ERPNext
	s.updateProgress(syncID, func(p *SyncProgress) {
		p.Status = "fetching_users"
		p.Message = "Fetching users from ERPNext..."
	})

	instance := erpnext.Instance{URL: cooperative.ApiUrl, APIKey: cooperative.ApiKey}
	users, err := s.fetchUsersWithRetry(ctx, instance)
	if err != nil {
		s.updateProgress(syncID, func(p *SyncProgress) {
			p.Status = "error"
			p.Message = fmt.Sprintf("Failed to fetch users: %v", err)
		})
		return result, fmt.Errorf("failed to fetch users: %w", err)
	}

	result.TotalRecords = len(users)
	s.updateProgress(syncID, func(p *SyncProgress) {
		p.Total = len(users)
		p.Status = "processing"
		p.Message = fmt.Sprintf("Processing %d users...", len(users))
	})

	// Process users in batches
	log.Printf("Starting batch processing for %d users with sync ID: %s", len(users), syncID)
	newCount, updateCount, failedCount, errors := s.processUsersInBatches(ctx, syncID, users, int64(cooperativeID))
	log.Printf("Batch processing completed for sync ID: %s. New: %d, Updated: %d, Failed: %d", syncID, newCount, updateCount, failedCount)

	result.NewRecords = newCount
	result.UpdatedRecords = updateCount
	result.FailedRecords = failedCount
	result.Duration = time.Since(startTime)
	result.Success = failedCount == 0
	result.Errors = errors

	// Final progress update with correct totals
	s.updateProgress(syncID, func(p *SyncProgress) {
		// Ensure final counts are accurate
		p.Processed = result.TotalRecords // Make sure processed equals total for 100%
		p.Failed = failedCount

		if result.Success {
			p.Status = "completed"
			p.Message = fmt.Sprintf("Sync completed successfully. New: %d, Updated: %d", newCount, updateCount)
		} else {
			p.Status = "completed_with_errors"
			p.Message = fmt.Sprintf("Sync completed with %d errors. New: %d, Updated: %d", failedCount, newCount, updateCount)
		}
	})

	// Log final progress state for debugging
	log.Printf("FINAL PROGRESS UPDATE for sync %s: Status=%s, Processed=%d, Total=%d, Failed=%d",
		syncID, "completed", result.TotalRecords, result.TotalRecords, failedCount)

	// Small delay to ensure UI polling captures the final state
	time.Sleep(time.Millisecond * 100)

	log.Printf("Agent sync completed for cooperative %d. New: %d, Updated: %d, Failed: %d, Duration: %v",
		cooperativeID, newCount, updateCount, failedCount, result.Duration)

	return result, nil
}

// fetchUsersWithRetry fetches users with retry logic
func (s *ImprovedAgentSyncService) fetchUsersWithRetry(ctx context.Context, instance erpnext.Instance) ([]erpnext.User, error) {
	var users []erpnext.User
	var err error

	for attempt := 0; attempt < s.config.RetryAttempts; attempt++ {
		if attempt > 0 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(s.config.RetryDelay):
			}
		}

		users, err = s.erpClient.GetUsers(instance)
		if err == nil {
			log.Printf("Fetched %d users. Total so far: %d", len(users), len(users))
			return users, nil
		}

		log.Printf("Attempt %d failed to fetch users: %v", attempt+1, err)
	}

	return nil, fmt.Errorf("failed to fetch users after %d attempts: %w", s.config.RetryAttempts, err)
}

// updateProgress safely updates progress for a sync operation
func (s *ImprovedAgentSyncService) updateProgress(syncID string, updateFn func(*SyncProgress)) {
	s.mu.Lock()
	defer s.mu.Unlock()
	if progress, exists := s.progress[syncID]; exists {
		updateFn(progress)
	}
}

// GetProgress returns the current progress for a sync operation
func (s *ImprovedAgentSyncService) GetProgress(syncID string) (*SyncProgress, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	progress, exists := s.progress[syncID]
	if exists {
		log.Printf("Progress for sync %s: Status=%s, Processed=%d, Total=%d, Message=%s",
			syncID, progress.Status, progress.Processed, progress.Total, progress.Message)
	} else {
		log.Printf("No progress found for sync ID: %s", syncID)
	}
	return progress, exists
}

// GetDB returns the database queries instance
func (s *ImprovedAgentSyncService) GetDB() *database.Queries {
	return s.db
}

// GetERPClient returns the ERP client instance
func (s *ImprovedAgentSyncService) GetERPClient() erpnext.Client {
	return s.erpClient
}

// processUsersInBatches processes users in batches with error handling
func (s *ImprovedAgentSyncService) processUsersInBatches(ctx context.Context, syncID string, users []erpnext.User, cooperativeID int64) (int, int, int, []string) {
	var newCount, updateCount, failedCount int
	var errors []string
	var mu sync.Mutex

	log.Printf("processUsersInBatches called with %d users, batch size: %d, sync ID: %s", len(users), s.config.BatchSize, syncID)

	// Process in batches
	for i := 0; i < len(users); i += s.config.BatchSize {
		end := i + s.config.BatchSize
		if end > len(users) {
			end = len(users)
		}
		batch := users[i:end]

		// Check context cancellation
		select {
		case <-ctx.Done():
			errors = append(errors, "Sync cancelled due to timeout")
			return newCount, updateCount, failedCount, errors
		default:
		}

		batchNew, batchUpdated, batchFailed, batchErrors := s.processBatchWithRetry(ctx, batch, cooperativeID)

		mu.Lock()
		newCount += batchNew
		updateCount += batchUpdated
		failedCount += batchFailed
		errors = append(errors, batchErrors...)
		mu.Unlock()

		// Update progress
		s.updateProgress(syncID, func(p *SyncProgress) {
			p.Processed = i + len(batch)
			p.Failed = failedCount
			p.Message = fmt.Sprintf("Processed %d/%d users (New: %d, Updated: %d, Failed: %d)",
				p.Processed, p.Total, newCount, updateCount, failedCount)
		})

		log.Printf("Progress updated for sync %s: %d/%d processed", syncID, i+len(batch), len(users))
	}

	return newCount, updateCount, failedCount, errors
}

// processBatchWithRetry processes a batch of users with retry logic
func (s *ImprovedAgentSyncService) processBatchWithRetry(ctx context.Context, users []erpnext.User, cooperativeID int64) (int, int, int, []string) {
	var newCount, updateCount, failedCount int
	var errors []string

	for _, user := range users {
		var err error
		var isNew bool

		// Retry logic for individual user processing
		for attempt := 0; attempt < s.config.RetryAttempts; attempt++ {
			if attempt > 0 {
				select {
				case <-ctx.Done():
					errors = append(errors, fmt.Sprintf("Processing cancelled for user %s", user.UserName))
					failedCount++
					goto nextUser
				case <-time.After(time.Millisecond * 100): // Short delay for individual retries
				}
			}

			isNew, err = s.upsertAgentImproved(ctx, user, cooperativeID)
			if err == nil {
				break
			}
		}

		if err != nil {
			failedCount++
			errorMsg := fmt.Sprintf("Failed to process user %s after %d attempts: %v", user.UserName, s.config.RetryAttempts, err)
			errors = append(errors, errorMsg)
			log.Printf("SYNC ERROR: %s", errorMsg)
		} else if isNew {
			newCount++
		} else {
			updateCount++
		}

	nextUser:
	}

	return newCount, updateCount, failedCount, errors
}

// upsertAgentImproved creates or updates an agent using improved logic
func (s *ImprovedAgentSyncService) upsertAgentImproved(ctx context.Context, user erpnext.User, cooperativeID int64) (bool, error) {
	// Get the underlying pgxpool.Pool
	pool, ok := s.db.DB().(*pgxpool.Pool)
	if !ok {
		return false, fmt.Errorf("database connection is not a pgxpool.Pool")
	}

	// Start a transaction
	tx, err := pool.Begin(ctx)
	if err != nil {
		return false, fmt.Errorf("error starting transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Create a new Queries instance that uses the transaction
	qtx := s.db.WithTx(tx)

	// Check if the user already exists
	existingUser, err := qtx.GetUserByEmailOrMobile(ctx, database.GetUserByEmailOrMobileParams{
		Email:        user.Email,
		MobileNumber: user.MobileNumber,
	})

	isNew := err != nil // If error, user doesn't exist (new)

	var userID int64
	if isNew {
		// Create new user
		createdUserID, err := qtx.CreateAgent(ctx, database.CreateAgentParams{
			Email:        user.Email,
			MobileNumber: user.MobileNumber,
		})
		if err != nil {
			// Handle unique constraint violations gracefully
			if pgErr, ok := err.(*pgconn.PgError); ok {
				if pgErr.Code == "23505" { // Unique constraint violation
					if strings.Contains(pgErr.Message, "app_user_email_key") {
						log.Printf("Skipping user %s due to duplicate email: %v", user.UserName, user.Email)
						return false, nil // Not an error, just skip
					} else if strings.Contains(pgErr.Message, "app_user_mobile_number_key") {
						log.Printf("Skipping user %s due to duplicate mobile number: %v", user.UserName, user.MobileNumber)
						return false, nil // Not an error, just skip
					}
				}
			}
			return false, fmt.Errorf("error creating user: %w", err)
		}
		userID = int64(createdUserID)
		log.Printf("Created new user %s with ID: %d", user.UserName, userID)
	} else {
		userID = int64(existingUser.ID)
		log.Printf("Found existing user %s with ID: %d", user.UserName, userID)
	}

	// Assign user to cooperative (this will update if already exists)
	idRole := enums.ResolveRoleEnum(user.UserType)
	err = qtx.AssignAgentToCooperative(ctx, database.AssignAgentToCooperativeParams{
		UserName:      user.UserName,
		ExternalID:    user.ExternalID,
		IDUser:        userID,
		IDCooperative: cooperativeID,
		IDRole:        int64(idRole),
	})
	if err != nil {
		return false, fmt.Errorf("error assigning user to cooperative: %w", err)
	}

	// Commit the transaction
	if err := tx.Commit(ctx); err != nil {
		return false, fmt.Errorf("error committing transaction: %w", err)
	}

	log.Printf("Successfully processed user %s (New: %t)", user.UserName, isNew)
	return isNew, nil
}
