package services

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/internal/erpnext"
)

// SyncProgress represents the progress of a sync operation
type SyncProgress struct {
	Total     int       `json:"total"`
	Processed int       `json:"processed"`
	Failed    int       `json:"failed"`
	Status    string    `json:"status"`
	Message   string    `json:"message"`
	StartTime time.Time `json:"start_time"`
}

// SyncResult represents the final result of a sync operation
type SyncResult struct {
	Success        bool          `json:"success"`
	TotalRecords   int           `json:"total_records"`
	NewRecords     int           `json:"new_records"`
	UpdatedRecords int           `json:"updated_records"`
	FailedRecords  int           `json:"failed_records"`
	Duration       time.Duration `json:"duration"`
	Errors         []string      `json:"errors,omitempty"`
}

// SyncConfig holds configuration for sync operations
type SyncConfig struct {
	BatchSize     int
	MaxWorkers    int
	Timeout       time.Duration
	RetryAttempts int
	RetryDelay    time.Duration
}

// DefaultSyncConfig returns default configuration
func DefaultSyncConfig() SyncConfig {
	return SyncConfig{
		BatchSize:     10, // Reduced for more frequent progress updates
		MaxWorkers:    3,  // Reduced to prevent overwhelming the API
		Timeout:       time.Minute * 10,
		RetryAttempts: 3,
		RetryDelay:    time.Second * 2,
	}
}

// ImprovedSyncService provides enhanced sync operations
type ImprovedSyncService struct {
	db        *database.Queries
	erpClient erpnext.Client
	config    SyncConfig
	progress  map[string]*SyncProgress
	mu        sync.RWMutex
}

// NewImprovedSyncService creates a new improved sync service
func NewImprovedSyncService(db *database.Queries, erpClient erpnext.Client, config SyncConfig) *ImprovedSyncService {
	return &ImprovedSyncService{
		db:        db,
		erpClient: erpClient,
		config:    config,
		progress:  make(map[string]*SyncProgress),
	}
}

// GetProgress returns the current progress for a sync operation
func (s *ImprovedSyncService) GetProgress(syncID string) (*SyncProgress, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	progress, exists := s.progress[syncID]
	if exists {
		log.Printf("Progress for sync %s: Status=%s, Processed=%d, Total=%d, Message=%s",
			syncID, progress.Status, progress.Processed, progress.Total, progress.Message)
	} else {
		log.Printf("No progress found for sync ID: %s", syncID)
	}
	return progress, exists
}

// updateProgress safely updates the progress
func (s *ImprovedSyncService) updateProgress(syncID string, update func(*SyncProgress)) {
	s.mu.Lock()
	defer s.mu.Unlock()
	if progress, exists := s.progress[syncID]; exists {
		update(progress)
	}
}

// SyncFarmersWithProgress performs farmer sync with progress tracking
func (s *ImprovedSyncService) SyncFarmersWithProgress(ctx context.Context, cooperativeID int, syncID string) (*SyncResult, error) {
	startTime := time.Now()

	// Initialize progress
	s.mu.Lock()
	s.progress[syncID] = &SyncProgress{
		Status:    "starting",
		Message:   "Initializing farmer sync...",
		StartTime: startTime,
	}
	s.mu.Unlock()

	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, s.config.Timeout)
	defer cancel()

	result := &SyncResult{
		Success: false,
	}

	// Get cooperative details
	s.updateProgress(syncID, func(p *SyncProgress) {
		p.Status = "fetching_cooperative"
		p.Message = "Fetching cooperative details..."
	})

	log.Printf("Attempting to get cooperative with ID: %d", cooperativeID)
	cooperative, err := s.db.GetCooperative(ctx, int32(cooperativeID))
	if err != nil {
		log.Printf("Failed to get cooperative %d: %v", cooperativeID, err)
		s.updateProgress(syncID, func(p *SyncProgress) {
			p.Status = "error"
			p.Message = fmt.Sprintf("Failed to get cooperative: %v", err)
		})
		return result, fmt.Errorf("failed to get cooperative: %w", err)
	}
	log.Printf("Successfully retrieved cooperative: %s (URL: %s)", cooperative.Name, cooperative.ApiUrl)

	// Fetch suppliers from ERPNext with retry logic
	s.updateProgress(syncID, func(p *SyncProgress) {
		p.Status = "fetching_suppliers"
		p.Message = "Fetching suppliers from ERPNext..."
	})

	instance := erpnext.Instance{URL: cooperative.ApiUrl, APIKey: cooperative.ApiKey}
	suppliers, err := s.fetchSuppliersWithRetry(ctx, instance)
	if err != nil {
		s.updateProgress(syncID, func(p *SyncProgress) {
			p.Status = "error"
			p.Message = fmt.Sprintf("Failed to fetch suppliers: %v", err)
		})
		return result, fmt.Errorf("failed to fetch suppliers: %w", err)
	}

	result.TotalRecords = len(suppliers)
	s.updateProgress(syncID, func(p *SyncProgress) {
		p.Total = len(suppliers)
		p.Status = "processing"
		p.Message = fmt.Sprintf("Processing %d suppliers...", len(suppliers))
	})

	// Process suppliers in batches
	log.Printf("Starting batch processing for %d suppliers with sync ID: %s", len(suppliers), syncID)
	newCount, updateCount, failedCount, errors := s.processSuppliersInBatches(ctx, syncID, suppliers, int64(cooperativeID))
	log.Printf("Batch processing completed for sync ID: %s. New: %d, Updated: %d, Failed: %d", syncID, newCount, updateCount, failedCount)

	result.NewRecords = newCount
	result.UpdatedRecords = updateCount
	result.FailedRecords = failedCount
	result.Duration = time.Since(startTime)
	result.Success = failedCount == 0
	result.Errors = errors

	// Final progress update with correct totals
	s.updateProgress(syncID, func(p *SyncProgress) {
		// Ensure final counts are accurate
		p.Processed = result.TotalRecords // Make sure processed equals total for 100%
		p.Failed = failedCount

		if result.Success {
			p.Status = "completed"
			p.Message = fmt.Sprintf("Sync completed successfully. New: %d, Updated: %d", newCount, updateCount)
		} else {
			p.Status = "completed_with_errors"
			p.Message = fmt.Sprintf("Sync completed with %d errors. New: %d, Updated: %d", failedCount, newCount, updateCount)
		}
	})

	// Log final progress state for debugging
	log.Printf("FINAL PROGRESS UPDATE for sync %s: Status=%s, Processed=%d, Total=%d, Failed=%d",
		syncID, "completed", result.TotalRecords, result.TotalRecords, failedCount)

	// Small delay to ensure UI polling captures the final state
	time.Sleep(time.Millisecond * 100)

	log.Printf("Farmer sync completed for cooperative %d. New: %d, Updated: %d, Failed: %d, Duration: %v",
		cooperativeID, newCount, updateCount, failedCount, result.Duration)

	return result, nil
}

// fetchSuppliersWithRetry fetches suppliers with retry logic
func (s *ImprovedSyncService) fetchSuppliersWithRetry(ctx context.Context, instance erpnext.Instance) ([]erpnext.Supplier, error) {
	var suppliers []erpnext.Supplier
	var err error

	for attempt := 0; attempt < s.config.RetryAttempts; attempt++ {
		if attempt > 0 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(s.config.RetryDelay):
			}
		}

		suppliers, err = s.erpClient.GetSuppliers(instance)
		if err == nil {
			return suppliers, nil
		}

		log.Printf("Attempt %d failed to fetch suppliers: %v", attempt+1, err)
	}

	return nil, fmt.Errorf("failed to fetch suppliers after %d attempts: %w", s.config.RetryAttempts, err)
}

// processSuppliersInBatches processes suppliers in batches with error handling
func (s *ImprovedSyncService) processSuppliersInBatches(ctx context.Context, syncID string, suppliers []erpnext.Supplier, cooperativeID int64) (int, int, int, []string) {
	var newCount, updateCount, failedCount int
	var errors []string
	var mu sync.Mutex

	log.Printf("processSuppliersInBatches called with %d suppliers, batch size: %d, sync ID: %s", len(suppliers), s.config.BatchSize, syncID)

	// Process in batches
	for i := 0; i < len(suppliers); i += s.config.BatchSize {
		end := i + s.config.BatchSize
		if end > len(suppliers) {
			end = len(suppliers)
		}
		batch := suppliers[i:end]

		// Check context cancellation
		select {
		case <-ctx.Done():
			errors = append(errors, "Sync cancelled due to timeout")
			return newCount, updateCount, failedCount, errors
		default:
		}

		batchNew, batchUpdated, batchFailed, batchErrors := s.processBatchWithRetry(ctx, batch, cooperativeID)

		mu.Lock()
		newCount += batchNew
		updateCount += batchUpdated
		failedCount += batchFailed
		errors = append(errors, batchErrors...)
		mu.Unlock()

		// Update progress
		s.updateProgress(syncID, func(p *SyncProgress) {
			p.Processed = i + len(batch)
			p.Failed = failedCount
			p.Message = fmt.Sprintf("Processed %d/%d suppliers (New: %d, Updated: %d, Failed: %d)",
				p.Processed, p.Total, newCount, updateCount, failedCount)
		})

		log.Printf("Progress updated for sync %s: %d/%d processed", syncID, i+len(batch), len(suppliers))
	}

	return newCount, updateCount, failedCount, errors
}

// processBatchWithRetry processes a batch of suppliers with retry logic
func (s *ImprovedSyncService) processBatchWithRetry(ctx context.Context, suppliers []erpnext.Supplier, cooperativeID int64) (int, int, int, []string) {
	var newCount, updateCount, failedCount int
	var errors []string

	for _, supplier := range suppliers {
		var err error
		var isNew bool

		// Retry logic for individual supplier processing
		for attempt := 0; attempt < s.config.RetryAttempts; attempt++ {
			if attempt > 0 {
				select {
				case <-ctx.Done():
					errors = append(errors, fmt.Sprintf("Processing cancelled for supplier %s", supplier.Name))
					failedCount++
					goto nextSupplier
				case <-time.After(time.Millisecond * 100): // Short delay for individual retries
				}
			}

			isNew, err = upsertFarmerImproved(ctx, s.db, supplier, cooperativeID)
			if err == nil {
				break
			}
		}

		if err != nil {
			failedCount++
			errorMsg := fmt.Sprintf("Failed to process supplier %s after %d attempts: %v", supplier.Name, s.config.RetryAttempts, err)
			errors = append(errors, errorMsg)
			log.Printf("SYNC ERROR: %s", errorMsg)
		} else if isNew {
			newCount++
		} else {
			updateCount++
		}

	nextSupplier:
	}

	return newCount, updateCount, failedCount, errors
}

// GetDB returns the database queries instance
func (s *ImprovedSyncService) GetDB() *database.Queries {
	return s.db
}

// GetERPClient returns the ERP client instance
func (s *ImprovedSyncService) GetERPClient() erpnext.Client {
	return s.erpClient
}

// upsertFarmerImproved uses the database UpsertFarmer method for better performance
func upsertFarmerImproved(ctx context.Context, db *database.Queries, supplier erpnext.Supplier, cooperativeID int64) (bool, error) {
	// Check if farmer already exists to determine if it's new or updated
	_, err := db.GetFarmerByExternalID(ctx, database.GetFarmerByExternalIDParams{
		IDExternal:    supplier.ExternalID,
		IDCooperative: cooperativeID,
	})

	isNew := err != nil // If error, farmer doesn't exist (new)

	// Helper function to convert string to *string
	stringPtr := func(s string) *string {
		if s == "" {
			return nil
		}
		return &s
	}

	// Use the database UpsertFarmer method for atomic operation
	err = db.UpsertFarmer(ctx, database.UpsertFarmerParams{
		IDExternal:    supplier.ExternalID,
		Name:          supplier.Name,
		Sno:           supplier.SNO,
		MobileNumber:  supplier.MobileNumber,
		CustomID:      stringPtr(supplier.CustomId),
		Location:      stringPtr(supplier.Location),
		Bank:          stringPtr(supplier.Bank),
		BankBranch:    stringPtr(supplier.BankBranch),
		AccountNumber: stringPtr(supplier.AccountNumber),
		Gender:        stringPtr(supplier.Gender),
		IDCooperative: cooperativeID,
		IDType:        1, // Default supplier type, could be made configurable
	})

	if err != nil {
		return false, fmt.Errorf("failed to upsert farmer: %w", err)
	}

	return isNew, nil
}
