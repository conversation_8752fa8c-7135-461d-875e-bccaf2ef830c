package handlers

import (
	"encoding/csv"
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/internal/erpnext"
	"github.com/itunza/africascongress/internal/services"
	"github.com/itunza/africascongress/templates/agents"
	"github.com/itunza/africascongress/templates/components"
	"github.com/itunza/africascongress/templates/farmers"
	"github.com/jackc/pgx/v5"
)

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	Total      int64       `json:"total"`
	TotalPages int         `json:"total_pages"`
	HasNext    bool        `json:"has_next"`
	HasPrev    bool        `json:"has_prev"`
}

type CooperativeHandler struct {
	db            *database.Queries
	erpNextClient erpnext.Client
	dbConn        *pgx.Conn
}

func NewCooperativeHandler(db *database.Queries, erpNextClient erpnext.Client, dbConn *pgx.Conn) *CooperativeHandler {
	return &CooperativeHandler{
		db:            db,
		erpNextClient: erpNextClient,
		dbConn:        dbConn,
	}
}
func (h *CooperativeHandler) Create(w http.ResponseWriter, r *http.Request) {

	// adjust to use both api json and form data

	log.Println("Creating cooperative")
	var coop database.CreateCooperativeParams

	if err := r.ParseForm(); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	coop.Name = r.FormValue("name")
	coop.ApiUrl = r.FormValue("api_url")
	coop.ApiKey = r.FormValue("api_key")
	coop.Address = r.FormValue("address")

	id, err := h.db.CreateCooperative(r.Context(), coop)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(map[string]int32{"id": id})
}

func (h *CooperativeHandler) List(w http.ResponseWriter, r *http.Request) {
	cooperativesWithCounts, err := h.db.ListCooperativesWithCounts(r.Context())
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Convert to the template struct format
	var cooperatives []components.CooperativeWithCounts
	for _, coop := range cooperativesWithCounts {
		cooperatives = append(cooperatives, components.CooperativeWithCounts{
			ID:            coop.ID,
			Name:          coop.Name,
			Address:       coop.Address,
			ApiUrl:        coop.ApiUrl,
			SupplierCount: coop.SupplierCount,
			AgentCount:    coop.AgentCount,
		})
	}

	// Render the cooperatives list
	err = components.CooperativesListWithCounts(cooperatives).Render(r.Context(), w)
	if err != nil {
		http.Error(w, "Failed to render cooperatives list", http.StatusInternalServerError)
		return
	}
}

func (h *CooperativeHandler) Get(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	coop, err := h.db.GetCooperative(r.Context(), int32(id))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(coop)
}

func (h *CooperativeHandler) Update(w http.ResponseWriter, r *http.Request) {
	var coop database.UpdateCooperativeParams
	if err := json.NewDecoder(r.Body).Decode(&coop); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	err := h.db.UpdateCooperative(r.Context(), coop)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (h *CooperativeHandler) Delete(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	err = h.db.DeleteCooperative(r.Context(), int32(id))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (h *CooperativeHandler) AddFarmer(w http.ResponseWriter, r *http.Request) {
	var farmer database.CreateFarmerParams
	if err := json.NewDecoder(r.Body).Decode(&farmer); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	id, err := h.db.CreateFarmer(r.Context(), farmer)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(map[string]int32{"id": id})
}

func (h *CooperativeHandler) ListFarmers(w http.ResponseWriter, r *http.Request) {
	coopIDStr := r.URL.Query().Get("cooperative_id")
	coopID, err := strconv.ParseInt(coopIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
		return
	}

	// Check if pagination is requested
	pageStr := r.URL.Query().Get("page")
	pageSizeStr := r.URL.Query().Get("page_size")

	if pageStr != "" && pageSizeStr != "" {
		// Paginated request
		page, err := strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			page = 1
		}

		pageSize, err := strconv.Atoi(pageSizeStr)
		if err != nil || pageSize < 1 || pageSize > 100 {
			pageSize = 20 // Default page size
		}

		offset := (page - 1) * pageSize

		// Get paginated farmers
		farmers, err := h.db.ListFarmersPaginated(r.Context(), database.ListFarmersPaginatedParams{
			IDCooperative: coopID,
			Limit:         int32(pageSize),
			Offset:        int32(offset),
		})
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// Get total count
		total, err := h.db.CountFarmers(r.Context(), coopID)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

		response := PaginatedResponse{
			Data:       farmers,
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
			HasNext:    page < totalPages,
			HasPrev:    page > 1,
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	} else {
		// Non-paginated request (backward compatibility)
		farmers, err := h.db.ListFarmers(r.Context(), coopID)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(farmers)
	}
}

func (h *CooperativeHandler) AddAgent(w http.ResponseWriter, r *http.Request) {
	// Parse the form data
	if err := r.ParseForm(); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Log the form data
	// log.Printf("Request body: %s", r.Form.Encode())

	// Extract and convert form data to the appropriate types
	username := r.FormValue("username")
	email := r.FormValue("email")
	phoneNumber := r.FormValue("phone_number")
	externalID := r.FormValue("external_id")
	cooperativeID, err := strconv.ParseInt(r.FormValue("cooperative_id"), 10, 64)
	if err != nil {
		http.Error(w, "Invalid cooperative_id", http.StatusBadRequest)
		return
	}
	roleID, err := strconv.ParseInt(r.FormValue("role_id"), 10, 64)
	if err != nil {
		http.Error(w, "Invalid role_id", http.StatusBadRequest)
		return
	}

	// Create a new agent in the database
	agentID, err := h.db.CreateAgent(r.Context(), database.CreateAgentParams{
		Email:        email,
		MobileNumber: phoneNumber,
	})
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Assign the agent to a cooperative
	err = h.db.AssignAgentToCooperative(r.Context(), database.AssignAgentToCooperativeParams{
		ExternalID:    externalID,
		IDUser:        int64(agentID), // Convert int32 to int64
		IDCooperative: cooperativeID,
		IDRole:        roleID,
		UserName:      username, // Use username from form data
	})
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Send the agent ID as a JSON response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]int32{"id": agentID})
}

func (h *CooperativeHandler) ListAgents(w http.ResponseWriter, r *http.Request) {
	coopIDStr := r.URL.Query().Get("cooperative_id")
	coopID, err := strconv.ParseInt(coopIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
		return
	}

	roleIDStr := r.URL.Query().Get("role_id")
	if roleIDStr != "" {
		roleID, err := strconv.ParseInt(roleIDStr, 10, 64)
		if err != nil {
			http.Error(w, "Invalid role ID", http.StatusBadRequest)
			return
		}
		agents, _ := h.db.ListAgents(r.Context(), database.ListAgentsParams{
			IDCooperative: coopID,
			IDRole:        roleID,
		})

		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		json.NewEncoder(w).Encode(agents)
	} else {
		// If no role_id is provided, list all agents for the cooperative
		agents, err := h.db.ListAllAgentsForCooperative(r.Context(), coopID)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		json.NewEncoder(w).Encode(agents)
	}
}

func (h *CooperativeHandler) UpdateFarmer(w http.ResponseWriter, r *http.Request) {
	var farmer database.UpdateFarmerParams

	// Parse the form data
	if err := r.ParseForm(); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	// Log the form data

	// Convert form data to UpdateFarmerParams
	// id64, err := strconv.ParseInt(r.FormValue("id"), 10, 32)
	// if err != nil {
	// 	http.Error(w, "Invalid id", http.StatusBadRequest)
	// 	return
	// }
	farmer.IDExternal = r.FormValue("id_external")
	farmer.Name = r.FormValue("name")
	farmer.Sno = r.FormValue("sno")
	farmer.MobileNumber = r.FormValue("phone_number")
	coopID64, err := strconv.ParseInt(r.FormValue("id_cooperative"), 10, 64)
	if err != nil {
		http.Error(w, "Invalid id_cooperative", http.StatusBadRequest)
		return
	}

	farmer.IDCooperative = coopID64

	err = h.db.UpdateFarmer(r.Context(), farmer)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (h *CooperativeHandler) DeleteFarmer(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	err = h.db.DeleteFarmer(r.Context(), int32(id))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (h *CooperativeHandler) BulkUploadFarmers(w http.ResponseWriter, r *http.Request) {
	file, _, err := r.FormFile("file")
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Parse CSV file
	records, err := csv.NewReader(file).ReadAll()
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Start a transaction
	tx, err := h.db.BeginTx(r.Context())
	if err != nil {
		http.Error(w, "Failed to start transaction", http.StatusInternalServerError)
		return
	}
	defer tx.Rollback(r.Context())

	// Create a new Queries instance with the transaction
	qtx := h.db.WithTx(tx)

	for _, record := range records[1:] { // Skip header row
		farmer := database.CreateFarmerParams{
			Name:          record[0],
			Sno:           record[1],
			MobileNumber:  record[2],
			IDCooperative: parseInt64(record[3]),
		}
		_, err := qtx.CreateFarmer(r.Context(), farmer)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
	}

	if err := tx.Commit(r.Context()); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

func (h *CooperativeHandler) BulkUploadAgents(w http.ResponseWriter, r *http.Request) {
	// Similar implementation to BulkUploadFarmers
	// Parse CSV, start transaction, create agents and assign to cooperative
}

func parseInt64(s string) int64 {
	i, _ := strconv.ParseInt(s, 10, 64)
	return i
}

func (h *CooperativeHandler) WebhookUpdate(w http.ResponseWriter, r *http.Request) {
	var update WebhookUpdate
	if err := json.NewDecoder(r.Body).Decode(&update); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	switch update.Type {
	case "farmer_update":
		err := h.db.UpdateFarmer(r.Context(), database.UpdateFarmerParams{
			IDExternal:    strconv.Itoa(int(update.Data.ID)),
			Name:          update.Data.Name,
			Sno:           update.Data.Sno,
			MobileNumber:  update.Data.PhoneNumber,
			IDCooperative: update.Data.IDCooperative,
		})
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
	case "farmer_delete":
		err := h.db.DeleteFarmer(r.Context(), update.Data.ID)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
	// Add more cases for other types of updates
	default:
		http.Error(w, "Unknown update type", http.StatusBadRequest)
		return
	}

	w.WriteHeader(http.StatusOK)
}

type WebhookUpdate struct {
	Type string      `json:"type"`
	Data WebhookData `json:"data"`
}

type WebhookData struct {
	ID            int32  `json:"id"`
	Name          string `json:"name"`
	Sno           string `json:"sno"`
	PhoneNumber   string `json:"phone_number"`
	IDCooperative int64  `json:"id_cooperative"`
}

func (h *CooperativeHandler) SearchFarmers(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query().Get("q")
	cooperativeIDStr := r.URL.Query().Get("cooperative_id")

	// If no cooperative is selected, return empty result
	if cooperativeIDStr == "" {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(PaginatedResponse{
			Data:       []database.Supplier{},
			Page:       1,
			PageSize:   20,
			Total:      0,
			TotalPages: 0,
			HasNext:    false,
			HasPrev:    false,
		})
		return
	}

	cooperativeID, err := strconv.ParseInt(cooperativeIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
		return
	}

	// Get pagination parameters
	pageStr := r.URL.Query().Get("page")
	pageSizeStr := r.URL.Query().Get("page_size")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20 // Default page size
	}

	offset := (page - 1) * pageSize

	// If no search query, return all farmers for the cooperative (paginated)
	if query == "" {
		farmers, err := h.db.ListFarmersPaginated(r.Context(), database.ListFarmersPaginatedParams{
			IDCooperative: cooperativeID,
			Limit:         int32(pageSize),
			Offset:        int32(offset),
		})
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		total, err := h.db.CountFarmers(r.Context(), cooperativeID)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

		response := PaginatedResponse{
			Data:       farmers,
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
			HasNext:    page < totalPages,
			HasPrev:    page > 1,
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}

	// Search farmers with the query (paginated)
	farmers, err := h.db.SearchFarmersPaginated(r.Context(), database.SearchFarmersPaginatedParams{
		Name:          "%" + query + "%",
		Sno:           "%" + query + "%",
		MobileNumber:  "%" + query + "%",
		IDCooperative: cooperativeID,
		Limit:         int32(pageSize),
		Offset:        int32(offset),
	})
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Get total count for search results
	total, err := h.db.CountSearchFarmers(r.Context(), database.CountSearchFarmersParams{
		Name:          "%" + query + "%",
		Sno:           "%" + query + "%",
		MobileNumber:  "%" + query + "%",
		IDCooperative: cooperativeID,
	})
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	response := PaginatedResponse{
		Data:       farmers,
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}

	// Return JSON response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (h *CooperativeHandler) EditFarmer(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid farmer ID", http.StatusBadRequest)
		return
	}

	farmer, err := h.db.GetFarmer(r.Context(), int32(id))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	err = farmers.Edit(farmer).Render(r.Context(), w)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	}
}

func (h *CooperativeHandler) RenderAddAgentForm(w http.ResponseWriter, r *http.Request) {
	cooperativeID := r.URL.Query().Get("cooperative_id")
	id, err := strconv.ParseInt(cooperativeID, 10, 32)
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	cooperative, err := h.db.GetCooperative(r.Context(), int32(id))
	if err != nil {
		http.Error(w, "Failed to fetch cooperative", http.StatusInternalServerError)
		return
	}

	agents.AddAgentForm(int64(cooperative.ID), cooperative.Name).Render(r.Context(), w)
}

// func (h *CooperativeHandler) RenderAddFarmerForm(w http.ResponseWriter, r *http.Request) {
// 	cooperativeID, err := strconv.ParseInt(r.URL.Query().Get("cooperative_id"), 10, 64)
// 	if err != nil {
// 		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
// 		return
// 	}

// 	cooperative, err := h.db.GetCooperative(r.Context(), cooperativeID)
// 	if err != nil {
// 		http.Error(w, "Failed to fetch cooperative", http.StatusInternalServerError)
// 		return
// 	}

//		farmers.AddFarmerForm(cooperativeID, cooperative.Name).Render(r.Context(), w)
//	}

func (h *CooperativeHandler) SyncFarmers(w http.ResponseWriter, r *http.Request) {
	coopIDStr := r.URL.Query().Get("cooperative_id")
	coopID, err := strconv.ParseInt(coopIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
		return
	}

	err = services.SyncFarmers(int(coopID), h.db, h.erpNextClient, r.Context())
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		json.NewEncoder(w).Encode("There was an error syncing farmers")
		return
	}

	json.NewEncoder(w).Encode("Sync successful")
}

func (h *CooperativeHandler) SyncAgents(w http.ResponseWriter, r *http.Request) {
	coopIDStr := r.URL.Query().Get("cooperative_id")
	coopID, err := strconv.ParseInt(coopIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
		return
	}

	err = services.SyncAgents(int(coopID), h.db, h.erpNextClient, r.Context())
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		json.NewEncoder(w).Encode("There was an error syncing agents")
		return
	}

	json.NewEncoder(w).Encode("Sync successful")
}
