package handlers

import (
	"encoding/json"
	"net/http"
	"os"

	"github.com/itunza/africascongress/internal/auth"
	"github.com/itunza/africascongress/internal/database"
	authtempl "github.com/itunza/africascongress/templates/auth"
)

type AuthHandler struct {
	db *database.Queries
}

func NewAuthHandler(db *database.Queries) *AuthHandler {
	return &AuthHandler{db: db}
}

func (h *AuthHandler) LoginPage(w http.ResponseWriter, r *http.Request) {
	authtempl.Login().Render(r.Context(), w)
}

func (h *AuthHandler) RegisterPage(w http.ResponseWriter, r *http.Request) {
	authtempl.Register().Render(r.Context(), w)
}

func (h *AuthHandler) Register(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodPost {
		contentType := r.Header.Get("Content-Type")
		if contentType == "application/json" {
			h.handleJSONRegister(w, r)
			return
		}
	}

	h.handleFormRegister(w, r)
}

func (h *AuthHandler) handleFormRegister(w http.ResponseWriter, r *http.Request) {
	adminPassword := r.FormValue("admin_password")
	username := r.FormValue("username")
	password := r.FormValue("password")

	// Validate admin password
	expectedAdminPassword := os.Getenv("ADMIN_PASSWORD")
	if expectedAdminPassword == "" {
		http.Error(w, "Admin password not configured", http.StatusInternalServerError)
		return
	}

	if adminPassword != expectedAdminPassword {
		http.Error(w, "Invalid admin password", http.StatusUnauthorized)
		return
	}

	hashedPassword, err := auth.HashPassword(password)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	err = h.db.CreateUser(r.Context(), database.CreateUserParams{
		Username: username,
		Password: hashedPassword,
	})
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	http.Redirect(w, r, "/login", http.StatusSeeOther)
}

func (h *AuthHandler) handleJSONRegister(w http.ResponseWriter, r *http.Request) {
	var registerRequest struct {
		AdminPassword string `json:"admin_password"`
		Username      string `json:"username"`
		Password      string `json:"password"`
	}

	err := json.NewDecoder(r.Body).Decode(&registerRequest)
	if err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	// Validate admin password
	expectedAdminPassword := os.Getenv("ADMIN_PASSWORD")
	if expectedAdminPassword == "" {
		http.Error(w, "Admin password not configured", http.StatusInternalServerError)
		return
	}

	if registerRequest.AdminPassword != expectedAdminPassword {
		http.Error(w, "Invalid admin password", http.StatusUnauthorized)
		return
	}

	hashedPassword, err := auth.HashPassword(registerRequest.Password)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	err = h.db.CreateUser(r.Context(), database.CreateUserParams{
		Username: registerRequest.Username,
		Password: hashedPassword,
	})
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := struct {
		Message string `json:"message"`
	}{
		Message: "User registered successfully",
	}

	w.Header().Set("Content-Type", "application/json")
	err = json.NewEncoder(w).Encode(response)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodPost {
		contentType := r.Header.Get("Content-Type")
		if contentType == "application/json" {
			h.handleJSONLogin(w, r)
			return
		}
	}

	h.handleFormLogin(w, r)
}

func (h *AuthHandler) handleFormLogin(w http.ResponseWriter, r *http.Request) {
	username := r.FormValue("username")
	password := r.FormValue("password")

	dbUser, err := h.db.GetUserByUsername(r.Context(), username)
	if err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	if !auth.CheckPasswordHash(password, dbUser.Password) {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	token, err := auth.GenerateToken(username)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Set the token as a cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "auth_token",
		Value:    token,
		HttpOnly: true,
		Path:     "/",
		MaxAge:   3600 * 24, // 1 day
	})

	// Respond with redirect for htmx
	w.Header().Set("HX-Redirect", "/")
	w.WriteHeader(http.StatusOK)
}

func (h *AuthHandler) handleJSONLogin(w http.ResponseWriter, r *http.Request) {
	var loginRequest struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	err := json.NewDecoder(r.Body).Decode(&loginRequest)
	if err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	dbUser, err := h.db.GetUserByUsername(r.Context(), loginRequest.Username)
	if err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	if !auth.CheckPasswordHash(loginRequest.Password, dbUser.Password) {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	token, err := auth.GenerateToken(loginRequest.Username)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := struct {
		Token string `json:"token"`
	}{
		Token: token,
	}

	w.Header().Set("Content-Type", "application/json")
	err = json.NewEncoder(w).Encode(response)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

func (h *AuthHandler) HandleLogout(w http.ResponseWriter, r *http.Request) {
	// Clear the auth_token cookie by setting MaxAge to -1
	http.SetCookie(w, &http.Cookie{
		Name:     "auth_token",
		Value:    "",
		HttpOnly: true,
		Path:     "/",
		MaxAge:   -1, // This will immediately expire the cookie
	})

	// Respond with redirect for htmx
	w.Header().Set("HX-Redirect", "/login")
	w.WriteHeader(http.StatusOK)
}
