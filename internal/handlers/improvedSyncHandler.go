package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/internal/erpnext"
	"github.com/itunza/africascongress/internal/services"
	"github.com/itunza/africascongress/templates/components"
)

// ImprovedSyncHandler provides enhanced sync operations with progress tracking
type ImprovedSyncHandler struct {
	syncService *services.ImprovedSyncService
}

// NewImprovedSyncHandler creates a new improved sync handler
func NewImprovedSyncHandler(db *database.Queries, erpClient erpnext.Client) *ImprovedSyncHandler {
	config := services.DefaultSyncConfig()
	syncService := services.NewImprovedSyncService(db, erpClient, config)
	
	return &ImprovedSyncHandler{
		syncService: syncService,
	}
}

// SyncFarmersAsync starts an asynchronous farmer sync operation
func (h *ImprovedSyncHandler) SyncFarmersAsync(w http.ResponseWriter, r *http.Request) {
	coopIDStr := r.URL.Query().Get("cooperative_id")
	coopID, err := strconv.ParseInt(coopIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
		return
	}

	// Generate unique sync ID
	syncID := fmt.Sprintf("farmers_%d_%d", coopID, time.Now().Unix())

	// Start sync in background
	go func() {
		result, err := h.syncService.SyncFarmersWithProgress(r.Context(), int(coopID), syncID)
		if err != nil {
			// Log error but don't fail the response since it's async
			fmt.Printf("Async sync failed: %v\n", err)
		} else {
			fmt.Printf("Async sync completed: %+v\n", result)
		}
	}()

	// Return sync ID for progress tracking
	response := map[string]interface{}{
		"sync_id": syncID,
		"message": "Sync started successfully",
		"status":  "started",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// GetSyncProgress returns the current progress of a sync operation
func (h *ImprovedSyncHandler) GetSyncProgress(w http.ResponseWriter, r *http.Request) {
	syncID := r.URL.Query().Get("sync_id")
	if syncID == "" {
		http.Error(w, "sync_id parameter is required", http.StatusBadRequest)
		return
	}

	progress, exists := h.syncService.GetProgress(syncID)
	if !exists {
		http.Error(w, "Sync operation not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(progress)
}

// RenderSyncProgress renders the sync progress as HTML component
func (h *ImprovedSyncHandler) RenderSyncProgress(w http.ResponseWriter, r *http.Request) {
	syncID := r.URL.Query().Get("sync_id")
	if syncID == "" {
		http.Error(w, "sync_id parameter is required", http.StatusBadRequest)
		return
	}

	progress, exists := h.syncService.GetProgress(syncID)
	if !exists {
		http.Error(w, "Sync operation not found", http.StatusNotFound)
		return
	}

	// Render progress component
	err := components.SyncProgress(*progress).Render(r.Context(), w)
	if err != nil {
		http.Error(w, "Failed to render progress", http.StatusInternalServerError)
		return
	}
}

// SyncFarmersWithProgress performs synchronous farmer sync with progress updates
func (h *ImprovedSyncHandler) SyncFarmersWithProgress(w http.ResponseWriter, r *http.Request) {
	coopIDStr := r.URL.Query().Get("cooperative_id")
	coopID, err := strconv.ParseInt(coopIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
		return
	}

	// Generate unique sync ID
	syncID := fmt.Sprintf("farmers_%d_%d", coopID, time.Now().Unix())

	// Perform sync
	result, err := h.syncService.SyncFarmersWithProgress(r.Context(), int(coopID), syncID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Return detailed result
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"sync_id": syncID,
		"result":  result,
		"message": "Sync completed",
	})
}

// Enhanced sync status component for better UI feedback
type SyncStatus struct {
	SyncID    string                   `json:"sync_id"`
	Progress  *services.SyncProgress   `json:"progress,omitempty"`
	Result    *services.SyncResult     `json:"result,omitempty"`
	Error     string                   `json:"error,omitempty"`
}

// GetSyncStatus returns comprehensive sync status
func (h *ImprovedSyncHandler) GetSyncStatus(w http.ResponseWriter, r *http.Request) {
	syncID := r.URL.Query().Get("sync_id")
	if syncID == "" {
		http.Error(w, "sync_id parameter is required", http.StatusBadRequest)
		return
	}

	status := SyncStatus{
		SyncID: syncID,
	}

	progress, exists := h.syncService.GetProgress(syncID)
	if exists {
		status.Progress = progress
	} else {
		status.Error = "Sync operation not found"
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// BatchSyncFarmers performs batch sync with configurable batch size
func (h *ImprovedSyncHandler) BatchSyncFarmers(w http.ResponseWriter, r *http.Request) {
	coopIDStr := r.URL.Query().Get("cooperative_id")
	coopID, err := strconv.ParseInt(coopIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid cooperative ID", http.StatusBadRequest)
		return
	}

	// Optional batch size parameter
	batchSizeStr := r.URL.Query().Get("batch_size")
	batchSize := 50 // default
	if batchSizeStr != "" {
		if bs, err := strconv.Atoi(batchSizeStr); err == nil && bs > 0 && bs <= 200 {
			batchSize = bs
		}
	}

	// Create custom config with specified batch size
	config := services.DefaultSyncConfig()
	config.BatchSize = batchSize

	// Create temporary sync service with custom config
	syncService := services.NewImprovedSyncService(h.syncService.GetDB(), h.syncService.GetERPClient(), config)
	
	syncID := fmt.Sprintf("batch_farmers_%d_%d", coopID, time.Now().Unix())

	// Start async sync
	go func() {
		result, err := syncService.SyncFarmersWithProgress(r.Context(), int(coopID), syncID)
		if err != nil {
			fmt.Printf("Batch sync failed: %v\n", err)
		} else {
			fmt.Printf("Batch sync completed: %+v\n", result)
		}
	}()

	response := map[string]interface{}{
		"sync_id":    syncID,
		"batch_size": batchSize,
		"message":    fmt.Sprintf("Batch sync started with batch size %d", batchSize),
		"status":     "started",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// CancelSync cancels an ongoing sync operation
func (h *ImprovedSyncHandler) CancelSync(w http.ResponseWriter, r *http.Request) {
	syncID := r.URL.Query().Get("sync_id")
	if syncID == "" {
		http.Error(w, "sync_id parameter is required", http.StatusBadRequest)
		return
	}

	// Update progress to cancelled status
	// Note: This is a simplified implementation. In a production system,
	// you'd want to implement proper cancellation with context.Context
	progress, exists := h.syncService.GetProgress(syncID)
	if !exists {
		http.Error(w, "Sync operation not found", http.StatusNotFound)
		return
	}

	if progress.Status == "completed" || progress.Status == "completed_with_errors" {
		http.Error(w, "Cannot cancel completed sync operation", http.StatusBadRequest)
		return
	}

	// Mark as cancelled (simplified - in real implementation, use context cancellation)
	progress.Status = "cancelled"
	progress.Message = "Sync operation was cancelled by user"

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"message": "Sync operation cancelled",
		"status":  "cancelled",
	})
}

// GetSyncHistory returns history of sync operations (simplified implementation)
func (h *ImprovedSyncHandler) GetSyncHistory(w http.ResponseWriter, r *http.Request) {
	// This is a simplified implementation
	// In a production system, you'd store sync history in the database
	
	response := map[string]interface{}{
		"message": "Sync history feature not yet implemented",
		"note":    "This would return historical sync operations from the database",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
