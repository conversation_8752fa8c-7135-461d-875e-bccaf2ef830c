<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Progress Bar</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Progress Bar Test</h1>
        
        <!-- Test Progress Bar -->
        <div class="bg-white rounded-lg shadow-md p-6" id="sync-progress">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Synchronization Progress</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    🔄 In Progress
                </span>
            </div>

            <!-- Status Message -->
            <div class="mb-4">
                <p class="text-sm text-gray-700 font-medium">Processing 450 / 952 suppliers...</p>
            </div>

            <!-- Progress Bar -->
            <div class="mb-6">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                    <span class="font-medium">Progress</span>
                    <span class="font-mono">450 / 952</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4 mb-2">
                    <div 
                        class="bg-gradient-to-r from-blue-500 to-blue-600 h-4 rounded-full transition-all duration-500 ease-out"
                        id="progress-bar-fill"
                        style="width: 47.3%"
                    ></div>
                </div>
                <div class="text-sm text-gray-600 text-center font-medium" id="progress-percentage">
                    47.3% complete
                </div>
            </div>

            <!-- Statistics Grid -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">952</div>
                    <div class="text-xs text-blue-500 font-medium">Total</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">450</div>
                    <div class="text-xs text-green-500 font-medium">Processed</div>
                </div>
                <div class="text-center p-3 bg-red-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">0</div>
                    <div class="text-xs text-red-500 font-medium">Failed</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-600">502</div>
                    <div class="text-xs text-gray-500 font-medium">Remaining</div>
                </div>
            </div>

            <!-- Timing Information -->
            <div class="text-xs text-gray-500 border-t pt-3 mb-4">
                <div class="flex justify-between">
                    <span>Started: 22:41:12</span>
                    <span>Elapsed: 00:03:15</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-2">
                <button class="px-4 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                    🚫 Cancel Sync
                </button>
            </div>
        </div>

        <!-- Test Progress Update -->
        <div class="mt-6">
            <button onclick="updateProgress()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                Simulate Progress Update
            </button>
        </div>
    </div>

    <script>
        let currentProgress = 47.3;
        
        function updateProgress() {
            currentProgress += 10;
            if (currentProgress > 100) currentProgress = 100;
            
            const progressBar = document.getElementById('progress-bar-fill');
            const progressText = document.getElementById('progress-percentage');
            
            if (progressBar) {
                progressBar.style.width = currentProgress + '%';
            }
            
            if (progressText) {
                progressText.textContent = currentProgress.toFixed(1) + '% complete';
            }
        }
    </script>
</body>
</html>
