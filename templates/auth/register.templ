package auth

templ Register() {
    <html>
        <head>
            <title>Register</title>
            <script src="https://unpkg.com/htmx.org@1.9.4"></script>
            <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100 flex items-center justify-center h-screen">
            <div class="bg-white p-8 rounded shadow-md w-96">
                <h2 class="text-2xl font-bold mb-4">Admin Registration</h2>
                <p class="text-sm text-gray-600 mb-4">Admin password required to register new users</p>
                <form hx-post="/api/register" hx-swap="outerHTML">
                    <div class="mb-4">
                        <label for="admin_password" class="block text-sm font-medium text-gray-600">Admin Password</label>
                        <input type="password" id="admin_password" name="admin_password" required
                               class="mt-1 px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1"/>
                    </div>
                    <div class="mb-4">
                        <label for="username" class="block text-sm font-medium text-gray-600">Username</label>
                        <input type="text" id="username" name="username" required
                               class="mt-1 px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1"/>
                    </div>
                    <div class="mb-4">
                        <label for="password" class="block text-sm font-medium text-gray-600">Password</label>
                        <input type="password" id="password" name="password" required
                               class="mt-1 px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1"/>
                    </div>
                    <button type="submit" class="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">Register</button>
                </form>
                <p class="mt-4 text-center">
                    Already have an account? <a href="/login" class="text-blue-500 hover:underline">Login</a>
                </p>
            </div>
        </body>
    </html>
}