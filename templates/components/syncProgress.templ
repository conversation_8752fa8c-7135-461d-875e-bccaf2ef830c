package components

import (
    "fmt"
    "strconv"
    "time"
    "github.com/itunza/africascongress/internal/services"
)

templ SyncProgress(progress services.SyncProgress) {
    <div class="bg-white rounded-lg shadow-md p-6" id="sync-progress">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Synchronization Progress</h3>
            <div class="flex items-center space-x-2">
                if progress.Status == "completed" {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        ✅ Completed
                    </span>
                } else if progress.Status == "error" {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        ❌ Error
                    </span>
                } else if progress.Status == "completed_with_errors" {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        ⚠️ Completed with Errors
                    </span>
                } else if progress.Status == "cancelled" {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        🚫 Cancelled
                    </span>
                } else {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        🔄 In Progress
                    </span>
                }
            </div>
        </div>

        <!-- Status Message -->
        <div class="mb-4">
            <p class="text-sm text-gray-700 font-medium">{ progress.Message }</p>
        </div>

        <!-- Progress Bar -->
        if progress.Total > 0 {
            <div class="mb-6">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                    <span class="font-medium">Progress</span>
                    <span class="font-mono">{ strconv.Itoa(progress.Processed) } / { strconv.Itoa(progress.Total) }</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4 mb-2">
                    <div
                        class="bg-gradient-to-r from-blue-500 to-blue-600 h-4 rounded-full transition-all duration-500 ease-out"
                        id="progress-bar-fill"
                    ></div>
                </div>
                <div class="text-sm text-gray-600 text-center font-medium" id="progress-percentage">
                    { fmt.Sprintf("%.1f%% complete", float64(progress.Processed)/float64(progress.Total)*100) }
                </div>
            </div>
        }

        <!-- Statistics Grid -->
        if progress.Total > 0 {
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">{ strconv.Itoa(progress.Total) }</div>
                    <div class="text-xs text-blue-500 font-medium">Total</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">{ strconv.Itoa(progress.Processed) }</div>
                    <div class="text-xs text-green-500 font-medium">Processed</div>
                </div>
                <div class="text-center p-3 bg-red-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">{ strconv.Itoa(progress.Failed) }</div>
                    <div class="text-xs text-red-500 font-medium">Failed</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-600">{ strconv.Itoa(progress.Total - progress.Processed) }</div>
                    <div class="text-xs text-gray-500 font-medium">Remaining</div>
                </div>
            </div>
        }

        <!-- Timing Information -->
        <div class="text-xs text-gray-500 border-t pt-3 mb-4">
            <div class="flex justify-between">
                <span>Started: { progress.StartTime.Format("15:04:05") }</span>
                if progress.Status == "completed" || progress.Status == "completed_with_errors" || progress.Status == "error" {
                    <span>Duration: { time.Since(progress.StartTime).Round(time.Second).String() }</span>
                } else {
                    <span>Elapsed: { time.Since(progress.StartTime).Round(time.Second).String() }</span>
                }
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-2">
            if progress.Status == "processing" || progress.Status == "fetching_suppliers" || progress.Status == "fetching_cooperative" {
                <button class="px-4 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                        onclick="cancelSync()">
                    🚫 Cancel Sync
                </button>
            }
            if progress.Status == "completed" || progress.Status == "completed_with_errors" || progress.Status == "error" {
                <button class="px-4 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                        onclick="refreshPage()">
                    🔄 Refresh Data
                </button>
            }
        </div>

        <!-- JavaScript to update progress bar -->
        if progress.Total > 0 {
            <script>
                // Update progress bar width
                const progressBar = document.getElementById('progress-bar-fill');
                if (progressBar) {
                    const percentage = { fmt.Sprintf("%.1f", float64(progress.Processed)/float64(progress.Total)*100) };
                    progressBar.style.width = percentage + '%';
                }
            </script>
        }

        <!-- Auto-refresh script for active syncs -->
        <script>
            // Check if sync is still active
            const syncStatus = "{ progress.Status }";
            const isActive = syncStatus !== "completed" &&
                           syncStatus !== "completed_with_errors" &&
                           syncStatus !== "error" &&
                           syncStatus !== "cancelled";

            if (isActive) {
                // Continue polling for active syncs
                setTimeout(function() {
                    if (typeof currentSyncId !== 'undefined') {
                        fetch('/api/sync/progress-component?sync_id=' + currentSyncId)
                            .then(response => response.text())
                            .then(html => {
                                document.getElementById('sync-progress').outerHTML = html;
                            })
                            .catch(error => console.error('Error updating progress:', error));
                    }
                }, 2000);
            } else {
                // Sync is complete - stop polling and clear any existing intervals
                console.log('Sync completed with status:', syncStatus);
                if (typeof progressInterval !== 'undefined') {
                    clearInterval(progressInterval);
                    progressInterval = undefined;
                }

                // Show completion message
                if (syncStatus === "completed") {
                    console.log('✅ Sync completed successfully!');
                } else if (syncStatus === "completed_with_errors") {
                    console.log('⚠️ Sync completed with some errors');
                } else if (syncStatus === "error") {
                    console.log('❌ Sync failed with errors');
                } else if (syncStatus === "cancelled") {
                    console.log('🚫 Sync was cancelled');
                }
            }
        </script>
    </div>
}

templ SyncProgressSimple(processed int, total int, status string, message string) {
    <div class="bg-white rounded-lg border p-4">
        <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">{ status }</span>
            if total > 0 {
                <span class="text-sm text-gray-500">{ strconv.Itoa(processed) }/{ strconv.Itoa(total) }</span>
            }
        </div>

        if total > 0 {
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div class="bg-blue-600 h-2 rounded-full"></div>
            </div>
        }

        <p class="text-xs text-gray-600">{ message }</p>
    </div>
}

templ SyncError(errorMessage string) {
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div>
                <h3 class="text-sm font-medium text-red-800">Synchronization Error</h3>
                <p class="text-sm text-red-700 mt-1">{ errorMessage }</p>
            </div>
        </div>
    </div>
}

templ SyncSuccess(message string) {
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <div>
                <h3 class="text-sm font-medium text-green-800">Synchronization Complete</h3>
                <p class="text-sm text-green-700 mt-1">{ message }</p>
            </div>
        </div>
    </div>
}
