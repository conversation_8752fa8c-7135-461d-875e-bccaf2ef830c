package components

import (
    "github.com/itunza/africascongress/internal/database"
    "strconv"

)

templ CooperativesList(cooperatives []database.Cooperative) {
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
            <thead class="bg-gray-100">
                <tr>
                    <th class="py-2 px-4 border-b text-left">ID</th>
                    <th class="py-2 px-4 border-b text-left">Name</th>
                    <th class="py-2 px-4 border-b text-left">Address</th>
                    <th class="py-2 px-4 border-b text-left">API URL</th>
                    <th class="py-2 px-4 border-b text-left">API Key</th>
                </tr>
            </thead>
            <tbody>
                for _, coop := range cooperatives {
                    <tr class="hover:bg-gray-50">
                        <td class="py-2 px-4 border-b">{ templ.JoinStringErrs(strconv.Itoa(int(coop.ID))) }</td>
                        <td class="py-2 px-4 border-b">{ coop.Name }</td>
                        <td class="py-2 px-4 border-b">{ coop.Address }</td>
                        <td class="py-2 px-4 border-b">{ coop.ApiUrl }</td>
                        <td class="py-2 px-4 border-b">{ coop.ApiKey }</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
}

type CooperativeWithCounts struct {
    ID            int32  `json:"id"`
    Name          string `json:"name"`
    Address       string `json:"address"`
    ApiUrl        string `json:"api_url"`
    SupplierCount int64  `json:"supplier_count"`
    AgentCount    int64  `json:"agent_count"`
}

templ CooperativesListWithCounts(cooperatives []CooperativeWithCounts) {
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                    <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API URL</th>
                    <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Suppliers</th>
                    <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agents</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                for _, coop := range cooperatives {
                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                        <td class="py-3 px-4 text-sm text-gray-900">{ templ.JoinStringErrs(strconv.Itoa(int(coop.ID))) }</td>
                        <td class="py-3 px-4 text-sm font-medium text-gray-900">{ coop.Name }</td>
                        <td class="py-3 px-4 text-sm text-gray-600">{ coop.Address }</td>
                        <td class="py-3 px-4 text-sm text-blue-600 hover:text-blue-800">
                            <a href={ templ.URL(coop.ApiUrl) } target="_blank" class="hover:underline">{ coop.ApiUrl }</a>
                        </td>
                        <td class="py-3 px-4 text-sm">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                { templ.JoinStringErrs(strconv.FormatInt(coop.SupplierCount, 10)) } suppliers
                            </span>
                        </td>
                        <td class="py-3 px-4 text-sm">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                { templ.JoinStringErrs(strconv.FormatInt(coop.AgentCount, 10)) } agents
                            </span>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
}