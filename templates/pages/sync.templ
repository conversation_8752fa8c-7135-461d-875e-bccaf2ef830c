// templates/pages/sync.templ
package pages

import (
    "github.com/itunza/africascongress/templates/layout"
    "github.com/itunza/africascongress/internal/database"
    "strconv"
)

templ Sync(cooperatives []database.Cooperative) {
    @layout.Base("Sync") {
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-4xl font-bold mb-8 text-gray-800">Data Synchronization</h1>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4 text-gray-700">Select Cooperative</h2>
                <div class="mb-4">
                    <label for="cooperative-select" class="block text-sm font-medium text-gray-600 mb-2">Choose a cooperative to synchronize</label>
                    <select id="cooperative-select" 
                            name="cooperative_id"
                            class="block w-full px-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                        <option value="">Select a cooperative</option>
                        for _, coop := range cooperatives {
                            <option value={strconv.FormatInt(int64(coop.ID), 10)}>{coop.Name}</option>
                        }
                    </select>
                </div>
                
                <!-- Sync Options -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-600 mb-2">Sync Options</label>
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="sync-mode" value="async" checked class="mr-2">
                            <span class="text-sm">Async (Recommended)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="sync-mode" value="sync" class="mr-2">
                            <span class="text-sm">Synchronous</span>
                        </label>
                    </div>
                </div>

                <div class="flex space-x-4 mt-6">
                    <button onclick="startFarmerSync()"
                            class="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-semibold px-4 py-2 rounded transition duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
                            id="sync-farmers-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
                        </svg>
                        <span id="sync-farmers-text">Synchronize Farmers</span>
                    </button>

                    <button onclick="startAgentSync()"
                            class="flex-1 bg-green-500 hover:bg-green-600 text-white font-semibold px-4 py-2 rounded transition duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
                            id="sync-agents-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
                        </svg>
                        <span id="sync-agents-text">Synchronize Agents</span>
                    </button>
                </div>
            </div>

            <div id="sync-result" class="bg-white rounded-lg shadow-md p-6 mt-8">
                <h2 class="text-2xl font-semibold mb-4 text-gray-700">Synchronization Results</h2>
                <p class="text-gray-600">Synchronization results will appear here.</p>
            </div>
        </div>

        <script>
            let currentSyncId = null;
            let progressInterval = null;

            function getSelectedCooperativeId() {
                const select = document.getElementById('cooperative-select');
                return select.value;
            }

            function getSyncMode() {
                const mode = document.querySelector('input[name="sync-mode"]:checked');
                return mode ? mode.value : 'async';
            }

            function showSyncResult(message, type = 'info') {
                const resultDiv = document.getElementById('sync-result');
                const alertClass = type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
                                  type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
                                  'bg-blue-50 border-blue-200 text-blue-800';

                resultDiv.innerHTML = `
                    <div class="${alertClass} border rounded-lg p-4">
                        <p class="text-sm">${message}</p>
                    </div>
                `;
            }

            function disableButtons() {
                document.getElementById('sync-farmers-btn').disabled = true;
                document.getElementById('sync-agents-btn').disabled = true;
            }

            function enableButtons() {
                document.getElementById('sync-farmers-btn').disabled = false;
                document.getElementById('sync-agents-btn').disabled = false;
            }

            function updateButtonText(buttonId, text) {
                const textElement = document.getElementById(buttonId + '-text');
                if (textElement) {
                    textElement.textContent = text;
                }
            }

            function startProgressTracking(syncId) {
                currentSyncId = syncId;

                // Clear any existing interval
                if (progressInterval) {
                    clearInterval(progressInterval);
                }

                // Start polling for progress
                progressInterval = setInterval(() => {
                    fetch(`/api/sync/progress-component?sync_id=${syncId}`)
                        .then(response => response.text())
                        .then(html => {
                            document.getElementById('sync-result').innerHTML = html;
                        })
                        .catch(error => {
                            console.error('Error fetching progress:', error);
                        });
                }, 2000);
            }

            function stopProgressTracking() {
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
                currentSyncId = null;
                enableButtons();
                updateButtonText('sync-farmers', 'Synchronize Farmers');
                updateButtonText('sync-agents', 'Synchronize Agents');
            }

            function startFarmerSync() {
                const cooperativeId = getSelectedCooperativeId();
                if (!cooperativeId) {
                    showSyncResult('Please select a cooperative first', 'error');
                    return;
                }

                const syncMode = getSyncMode();
                disableButtons();
                updateButtonText('sync-farmers', 'Starting...');

                const endpoint = syncMode === 'async' ?
                    '/api/sync/farmers-async' :
                    '/api/sync/farmers-progress';

                fetch(`${endpoint}?cooperative_id=${cooperativeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.sync_id) {
                        showSyncResult(`Farmer sync started successfully (ID: ${data.sync_id})`, 'success');
                        startProgressTracking(data.sync_id);
                        updateButtonText('sync-farmers', 'Syncing...');
                    } else {
                        showSyncResult('Failed to start sync', 'error');
                        enableButtons();
                        updateButtonText('sync-farmers', 'Synchronize Farmers');
                    }
                })
                .catch(error => {
                    showSyncResult(`Error starting sync: ${error.message}`, 'error');
                    enableButtons();
                    updateButtonText('sync-farmers', 'Synchronize Farmers');
                });
            }

            function startAgentSync() {
                const cooperativeId = getSelectedCooperativeId();
                if (!cooperativeId) {
                    showSyncResult('Please select a cooperative first', 'error');
                    return;
                }

                disableButtons();
                updateButtonText('sync-agents', 'Starting...');

                // Use the existing agent sync endpoint for now
                fetch(`/api/cooperatives/sync-agents?cooperative_id=${cooperativeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    showSyncResult('Agent sync completed successfully', 'success');
                    enableButtons();
                    updateButtonText('sync-agents', 'Synchronize Agents');
                })
                .catch(error => {
                    showSyncResult(`Error syncing agents: ${error.message}`, 'error');
                    enableButtons();
                    updateButtonText('sync-agents', 'Synchronize Agents');
                });
            }

            function cancelSync() {
                if (currentSyncId) {
                    fetch(`/api/sync/cancel?sync_id=${currentSyncId}`, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        showSyncResult('Sync operation cancelled', 'info');
                        stopProgressTracking();
                    })
                    .catch(error => {
                        console.error('Error cancelling sync:', error);
                    });
                }
            }

            function refreshPage() {
                window.location.reload();
            }

            // Clean up on page unload
            window.addEventListener('beforeunload', () => {
                stopProgressTracking();
            });
        </script>
    }
}