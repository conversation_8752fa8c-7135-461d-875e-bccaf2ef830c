// Code generated by templ - DO NOT EDIT.

// templ: version: v0.2.778
// templates/pages/sync.templ

package pages

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/templates/layout"
	"strconv"
)

func Sync(cooperatives []database.Cooperative) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Var2 := templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
			templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
			templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
			if !templ_7745c5c3_IsBuffer {
				defer func() {
					templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err == nil {
						templ_7745c5c3_Err = templ_7745c5c3_BufErr
					}
				}()
			}
			ctx = templ.InitializeContext(ctx)
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<div class=\"container mx-auto px-4 py-8\"><h1 class=\"text-4xl font-bold mb-8 text-gray-800\">Data Synchronization</h1><div class=\"bg-white rounded-lg shadow-md p-6 mb-8\"><h2 class=\"text-2xl font-semibold mb-4 text-gray-700\">Select Cooperative</h2><div class=\"mb-4\"><label for=\"cooperative-select\" class=\"block text-sm font-medium text-gray-600 mb-2\">Choose a cooperative to synchronize</label> <select id=\"cooperative-select\" name=\"cooperative_id\" class=\"block w-full px-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm\"><option value=\"\">Select a cooperative</option> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			for _, coop := range cooperatives {
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<option value=\"")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var3 string
				templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(strconv.FormatInt(int64(coop.ID), 10))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `templates/pages/sync.templ`, Line: 24, Col: 80}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var4 string
				templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(coop.Name)
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `templates/pages/sync.templ`, Line: 24, Col: 92}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("</option>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("</select></div><!-- Sync Options --><div class=\"mb-4\"><label class=\"block text-sm font-medium text-gray-600 mb-2\">Sync Options</label><div class=\"flex items-center space-x-4\"><label class=\"flex items-center\"><input type=\"radio\" name=\"sync-mode\" value=\"async\" checked class=\"mr-2\"> <span class=\"text-sm\">Async (Recommended)</span></label> <label class=\"flex items-center\"><input type=\"radio\" name=\"sync-mode\" value=\"sync\" class=\"mr-2\"> <span class=\"text-sm\">Synchronous</span></label></div></div><div class=\"flex space-x-4 mt-6\"><button onclick=\"startFarmerSync()\" class=\"flex-1 bg-blue-500 hover:bg-blue-600 text-white font-semibold px-4 py-2 rounded transition duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed\" id=\"sync-farmers-btn\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 inline-block mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path d=\"M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z\"></path></svg> <span id=\"sync-farmers-text\">Synchronize Farmers</span></button> <button onclick=\"startAgentSync()\" class=\"flex-1 bg-green-500 hover:bg-green-600 text-white font-semibold px-4 py-2 rounded transition duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed\" id=\"sync-agents-btn\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 inline-block mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path d=\"M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z\"></path></svg> <span id=\"sync-agents-text\">Synchronize Agents</span></button></div></div><div id=\"sync-result\" class=\"bg-white rounded-lg shadow-md p-6 mt-8\"><h2 class=\"text-2xl font-semibold mb-4 text-gray-700\">Synchronization Results</h2><p class=\"text-gray-600\">Synchronization results will appear here.</p></div></div><script>\n            let currentSyncId = null;\n            let progressInterval = null;\n\n            function getSelectedCooperativeId() {\n                const select = document.getElementById('cooperative-select');\n                return select.value;\n            }\n\n            function getSyncMode() {\n                const mode = document.querySelector('input[name=\"sync-mode\"]:checked');\n                return mode ? mode.value : 'async';\n            }\n\n            function showSyncResult(message, type = 'info') {\n                const resultDiv = document.getElementById('sync-result');\n                const alertClass = type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :\n                                  type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :\n                                  'bg-blue-50 border-blue-200 text-blue-800';\n\n                resultDiv.innerHTML = `\n                    <div class=\"${alertClass} border rounded-lg p-4\">\n                        <p class=\"text-sm\">${message}</p>\n                    </div>\n                `;\n            }\n\n            function disableButtons() {\n                document.getElementById('sync-farmers-btn').disabled = true;\n                document.getElementById('sync-agents-btn').disabled = true;\n            }\n\n            function enableButtons() {\n                document.getElementById('sync-farmers-btn').disabled = false;\n                document.getElementById('sync-agents-btn').disabled = false;\n            }\n\n            function updateButtonText(buttonId, text) {\n                const textElement = document.getElementById(buttonId + '-text');\n                if (textElement) {\n                    textElement.textContent = text;\n                }\n            }\n\n            function startProgressTracking(syncId) {\n                currentSyncId = syncId;\n\n                // Clear any existing interval\n                if (progressInterval) {\n                    clearInterval(progressInterval);\n                }\n\n                // Start polling for progress\n                progressInterval = setInterval(() => {\n                    // Try to get progress data first\n                fetch(`/api/sync/progress?sync_id=${syncId}`)\n                    .then(response => response.json())\n                    .then(progressData => {\n                        console.log('Progress data:', progressData);\n\n                        // Check if sync is complete and stop polling if needed\n                        if (checkSyncCompletion(progressData)) {\n                            return; // Stop here if sync is complete\n                        }\n\n                        // Try to get the HTML component\n                        fetch(`/api/sync/progress-component?sync_id=${syncId}`)\n                            .then(response => response.text())\n                            .then(html => {\n                                console.log('Progress HTML received:', html.substring(0, 200) + '...');\n                                document.getElementById('sync-result').innerHTML = html;\n                            })\n                            .catch(error => {\n                                console.error('Error fetching progress component:', error);\n                                // Fallback: create simple progress display\n                                createSimpleProgressDisplay(progressData);\n                            });\n                    })\n                    .catch(error => {\n                        console.error('Error fetching progress data:', error);\n                    });\n                }, 1000); // Reduced to 1 second for faster updates\n            }\n\n            function stopProgressTracking() {\n                if (progressInterval) {\n                    clearInterval(progressInterval);\n                    progressInterval = null;\n                    console.log('🛑 Progress tracking stopped');\n                }\n                currentSyncId = null;\n                enableButtons();\n                updateButtonText('sync-farmers', 'Synchronize Farmers');\n                updateButtonText('sync-agents', 'Synchronize Agents');\n            }\n\n            function checkSyncCompletion(progressData) {\n                const completedStatuses = ['completed', 'completed_with_errors', 'error', 'cancelled'];\n                if (completedStatuses.includes(progressData.status)) {\n                    stopProgressTracking();\n                    console.log('🏁 Sync finished with status:', progressData.status);\n                    return true;\n                }\n                return false;\n            }\n\n            function startFarmerSync() {\n                const cooperativeId = getSelectedCooperativeId();\n                if (!cooperativeId) {\n                    showSyncResult('Please select a cooperative first', 'error');\n                    return;\n                }\n\n                const syncMode = getSyncMode();\n                disableButtons();\n                updateButtonText('sync-farmers', 'Starting...');\n\n                const endpoint = syncMode === 'async' ?\n                    '/api/sync/farmers-async' :\n                    '/api/sync/farmers-progress';\n\n                fetch(`${endpoint}?cooperative_id=${cooperativeId}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                    }\n                })\n                .then(response => response.json())\n                .then(data => {\n                    if (data.sync_id) {\n                        showSyncResult(`Farmer sync started successfully (ID: ${data.sync_id})`, 'success');\n                        startProgressTracking(data.sync_id);\n                        updateButtonText('sync-farmers', 'Syncing...');\n                    } else {\n                        showSyncResult('Failed to start sync', 'error');\n                        enableButtons();\n                        updateButtonText('sync-farmers', 'Synchronize Farmers');\n                    }\n                })\n                .catch(error => {\n                    showSyncResult(`Error starting sync: ${error.message}`, 'error');\n                    enableButtons();\n                    updateButtonText('sync-farmers', 'Synchronize Farmers');\n                });\n            }\n\n            function startAgentSync() {\n                const cooperativeId = getSelectedCooperativeId();\n                if (!cooperativeId) {\n                    showSyncResult('Please select a cooperative first', 'error');\n                    return;\n                }\n\n                disableButtons();\n                updateButtonText('sync-agents', 'Starting...');\n\n                // Use the existing agent sync endpoint for now\n                fetch(`/api/cooperatives/sync-agents?cooperative_id=${cooperativeId}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                    }\n                })\n                .then(response => response.json())\n                .then(data => {\n                    showSyncResult('Agent sync completed successfully', 'success');\n                    enableButtons();\n                    updateButtonText('sync-agents', 'Synchronize Agents');\n                })\n                .catch(error => {\n                    showSyncResult(`Error syncing agents: ${error.message}`, 'error');\n                    enableButtons();\n                    updateButtonText('sync-agents', 'Synchronize Agents');\n                });\n            }\n\n            function cancelSync() {\n                if (currentSyncId) {\n                    fetch(`/api/sync/cancel?sync_id=${currentSyncId}`, {\n                        method: 'POST'\n                    })\n                    .then(response => response.json())\n                    .then(data => {\n                        showSyncResult('Sync operation cancelled', 'info');\n                        stopProgressTracking();\n                    })\n                    .catch(error => {\n                        console.error('Error cancelling sync:', error);\n                    });\n                }\n            }\n\n            function refreshPage() {\n                window.location.reload();\n            }\n\n            function createSimpleProgressDisplay(progressData) {\n                // Check if sync is complete and stop polling\n                checkSyncCompletion(progressData);\n\n                const percentage = progressData.total > 0 ? (progressData.processed / progressData.total * 100).toFixed(1) : 0;\n                const statusColor = progressData.status === 'completed' ? 'green' :\n                                   progressData.status === 'error' ? 'red' : 'blue';\n\n                const html = `\n                    <div class=\"bg-white rounded-lg shadow-md p-6\">\n                        <div class=\"flex items-center justify-between mb-4\">\n                            <h3 class=\"text-lg font-semibold text-gray-800\">Synchronization Progress</h3>\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${statusColor}-100 text-${statusColor}-800\">\n                                ${progressData.status}\n                            </span>\n                        </div>\n\n                        <div class=\"mb-4\">\n                            <p class=\"text-sm text-gray-700 font-medium\">${progressData.message}</p>\n                        </div>\n\n                        ${progressData.total > 0 ? `\n                        <div class=\"mb-6\">\n                            <div class=\"flex justify-between text-sm text-gray-600 mb-2\">\n                                <span class=\"font-medium\">Progress</span>\n                                <span class=\"font-mono\">${progressData.processed} / ${progressData.total}</span>\n                            </div>\n                            <div class=\"w-full bg-gray-200 rounded-full h-4 mb-2\">\n                                <div class=\"bg-gradient-to-r from-blue-500 to-blue-600 h-4 rounded-full transition-all duration-500 ease-out\"\n                                     style=\"width: ${percentage}%\"></div>\n                            </div>\n                            <div class=\"text-sm text-gray-600 text-center font-medium\">\n                                ${percentage}% complete\n                            </div>\n                        </div>\n\n                        <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n                            <div class=\"text-center p-3 bg-blue-50 rounded-lg\">\n                                <div class=\"text-2xl font-bold text-blue-600\">${progressData.total}</div>\n                                <div class=\"text-xs text-blue-500 font-medium\">Total</div>\n                            </div>\n                            <div class=\"text-center p-3 bg-green-50 rounded-lg\">\n                                <div class=\"text-2xl font-bold text-green-600\">${progressData.processed}</div>\n                                <div class=\"text-xs text-green-500 font-medium\">Processed</div>\n                            </div>\n                            <div class=\"text-center p-3 bg-red-50 rounded-lg\">\n                                <div class=\"text-2xl font-bold text-red-600\">${progressData.failed}</div>\n                                <div class=\"text-xs text-red-500 font-medium\">Failed</div>\n                            </div>\n                            <div class=\"text-center p-3 bg-gray-50 rounded-lg\">\n                                <div class=\"text-2xl font-bold text-gray-600\">${progressData.total - progressData.processed}</div>\n                                <div class=\"text-xs text-gray-500 font-medium\">Remaining</div>\n                            </div>\n                        </div>\n                        ` : ''}\n\n                        <div class=\"text-xs text-gray-500 border-t pt-3\">\n                            <div class=\"flex justify-between\">\n                                <span>Started: ${new Date(progressData.start_time).toLocaleTimeString()}</span>\n                                <span>Status: ${progressData.status}</span>\n                            </div>\n                        </div>\n                    </div>\n                `;\n\n                document.getElementById('sync-result').innerHTML = html;\n            }\n\n            // Clean up on page unload\n            window.addEventListener('beforeunload', () => {\n                stopProgressTracking();\n            });\n        </script>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			return templ_7745c5c3_Err
		})
		templ_7745c5c3_Err = layout.Base("Sync").Render(templ.WithChildren(ctx, templ_7745c5c3_Var2), templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return templ_7745c5c3_Err
	})
}

var _ = templruntime.GeneratedTemplate
