// templates/pages/farmers.templ
package pages

import (
    "github.com/itunza/africascongress/templates/layout"
     "strconv"
    "github.com/itunza/africascongress/internal/database"
)

templ Farmers(cooperatives []database.Cooperative) {
    @layout.Base("Farmers") {
        <div class="container mx-auto px-4 py-8">
            <!-- Header Section -->
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800">Farmers Management</h1>
                <div class="flex space-x-3">
                    <button id="refresh-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Refresh</span>
                    </button>
                    <button id="export-btn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Export</span>
                    </button>
                </div>
            </div>

            <!-- Cooperative Selection & Search Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Cooperative Selection -->
                    <div>
                        <label for="cooperative-select" class="block text-sm font-medium text-gray-700 mb-2">Select Cooperative</label>
                        <select id="cooperative-select" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                hx-get="/api/cooperatives/search-farmers"
                                hx-target="#farmer-list"
                                hx-trigger="change"
                                hx-include="[name='cooperative_id'], [name='page'], [name='page_size']">
                            <option value="">Select a cooperative</option>
                            for _, coop := range cooperatives {
                                <option value={strconv.Itoa(int(coop.ID))}>{coop.Name}</option>
                            }
                        </select>
                        <input type="hidden" name="cooperative_id" value="">
                        <input type="hidden" name="page" value="1">
                        <input type="hidden" name="page_size" value="20">
                    </div>

                    <!-- Search Section -->
                    <div>
                        <label for="search-input" class="block text-sm font-medium text-gray-700 mb-2">Search Farmers</label>
                        <div class="relative">
                            <input type="text" id="search-input" name="q" placeholder="Search by name, SNO, or phone..."
                                   class="w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   hx-get="/api/cooperatives/search-farmers"
                                   hx-target="#farmer-list"
                                   hx-trigger="keyup changed delay:300ms"
                                   hx-include="[name='cooperative_id'], [name='q'], [name='page'], [name='page_size']"
                                   hx-indicator="#search-loading">
                            <svg class="absolute left-3 top-3.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <!-- Loading indicator -->
                            <div id="search-loading" class="absolute right-3 top-3.5 htmx-indicator">
                                <svg class="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                            <!-- Search status indicator -->
                            <div id="search-status" class="absolute right-10 top-3.5 hidden">
                                <div class="flex items-center space-x-1 text-xs text-gray-500">
                                    <span id="search-count">0</span>
                                    <span>results</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Row -->
                <div class="mt-6 flex flex-wrap gap-3">
                    <button id="add-farmer-btn" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span>Add Farmer</span>
                    </button>
                    <button id="bulk-import-btn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <span>Bulk Import</span>
                    </button>
                    <button id="sync-farmers-btn" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Sync with ERP</span>
                    </button>
                </div>
            </div>

            <!-- Farmer List Section -->
            <div id="farmer-list" class="bg-white rounded-lg shadow-md">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                        <div id="farmer-stats" class="text-sm text-gray-500"></div>
                    </div>
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No farmers to display</h3>
                        <p class="mt-1 text-sm text-gray-500">Please select a cooperative to view farmers.</p>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let currentCooperativeId = '';
            let allFarmers = [];
            let currentPage = 1;
            let pageSize = 20;
            let totalPages = 0;
            let totalFarmers = 0;
            let isSearching = false;
            let currentSearchQuery = '';

            // Initialize page functionality
            document.addEventListener('DOMContentLoaded', function() {
                initializeEventListeners();
            });

            function initializeEventListeners() {
                // Cooperative selection handler
                document.getElementById('cooperative-select').addEventListener('change', function() {
                    currentCooperativeId = this.value;
                    document.querySelector('[name="cooperative_id"]').value = this.value;

                    // Reset pagination when changing cooperative
                    currentPage = 1;
                    document.querySelector('[name="page"]').value = '1';
                    isSearching = false;

                    // Enable/disable action buttons based on selection
                    toggleActionButtons(!!this.value);

                    // Clear search when changing cooperative
                    document.getElementById('search-input').value = '';
                    currentSearchQuery = '';
                });

                // Action button handlers
                document.getElementById('refresh-btn').addEventListener('click', refreshFarmers);
                document.getElementById('export-btn').addEventListener('click', exportFarmers);
                document.getElementById('add-farmer-btn').addEventListener('click', showAddFarmerModal);
                document.getElementById('bulk-import-btn').addEventListener('click', showBulkImportModal);
                document.getElementById('sync-farmers-btn').addEventListener('click', syncFarmers);
            }

            function toggleActionButtons(enabled) {
                const buttons = ['add-farmer-btn', 'bulk-import-btn', 'sync-farmers-btn'];
                buttons.forEach(btnId => {
                    const btn = document.getElementById(btnId);
                    btn.disabled = !enabled;
                    if (enabled) {
                        btn.classList.remove('opacity-50', 'cursor-not-allowed');
                    } else {
                        btn.classList.add('opacity-50', 'cursor-not-allowed');
                    }
                });
            }

            function refreshFarmers() {
                if (currentCooperativeId) {
                    htmx.trigger('#cooperative-select', 'change');
                    showNotification('Farmers list refreshed', 'success');
                }
            }

            function exportFarmers() {
                if (!currentCooperativeId) {
                    showNotification('Please select a cooperative first', 'error');
                    return;
                }

                // Create CSV content
                const csvContent = generateCSV(allFarmers);
                downloadCSV(csvContent, `farmers_${currentCooperativeId}_${new Date().toISOString().split('T')[0]}.csv`);
                showNotification('Farmers data exported successfully', 'success');
            }

            function generateCSV(farmers) {
                const headers = ['Name', 'SNO', 'Phone Number', 'Location', 'Gender', 'Bank', 'Account Number'];
                const csvRows = [headers.join(',')];

                farmers.forEach(farmer => {
                    const row = [
                        `"${farmer.name || ''}"`,
                        `"${farmer.sno || ''}"`,
                        `"${farmer.mobile_number || ''}"`,
                        `"${farmer.location || ''}"`,
                        `"${farmer.gender || ''}"`,
                        `"${farmer.bank || ''}"`,
                        `"${farmer.account_number || ''}"`
                    ];
                    csvRows.push(row.join(','));
                });

                return csvRows.join('\n');
            }

            function downloadCSV(content, filename) {
                const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                if (link.download !== undefined) {
                    const url = URL.createObjectURL(blob);
                    link.setAttribute('href', url);
                    link.setAttribute('download', filename);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }

            function showAddFarmerModal() {
                showNotification('Add farmer functionality coming soon', 'info');
            }

            function showBulkImportModal() {
                showNotification('Bulk import functionality coming soon', 'info');
            }

            function syncFarmers() {
                if (!currentCooperativeId) {
                    showNotification('Please select a cooperative first', 'error');
                    return;
                }

                showNotification('Syncing farmers with ERP...', 'info');

                fetch(`/api/cooperatives/sync-farmers?cooperative_id=${currentCooperativeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    showNotification('Farmers synced successfully', 'success');
                    refreshFarmers();
                })
                .catch(error => {
                    showNotification('Error syncing farmers: ' + error.message, 'error');
                });
            }

            function showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${getNotificationClasses(type)}`;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Auto remove after 3 seconds
                setTimeout(() => {
                    notification.classList.add('opacity-0', 'translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            function getNotificationClasses(type) {
                switch (type) {
                    case 'success': return 'bg-green-500 text-white';
                    case 'error': return 'bg-red-500 text-white';
                    case 'warning': return 'bg-yellow-500 text-white';
                    default: return 'bg-blue-500 text-white';
                }
            }

            // HTMX event handlers
            htmx.on('#farmer-list', 'htmx:afterSwap', function(evt) {
                console.log('HTMX afterSwap triggered');
                const farmerList = evt.detail.elt;
                let response = {};

                try {
                    console.log('Raw response:', farmerList.textContent);
                    response = JSON.parse(farmerList.textContent);
                    console.log('Parsed response:', response);

                    // Handle both paginated and non-paginated responses
                    if (response.data !== undefined) {
                        // Paginated response
                        allFarmers = response.data;
                        currentPage = response.page;
                        pageSize = response.page_size;
                        totalPages = response.total_pages;
                        totalFarmers = response.total;

                        updateFarmerStats(response.total, response.data.length);
                        updateSearchStatus(response.data.length);

                        if (response.data.length === 0) {
                            renderEmptyState();
                        } else {
                            renderFarmerTable(response.data);
                            renderPagination(response);
                        }
                    } else {
                        // Non-paginated response (backward compatibility)
                        allFarmers = response;
                        updateFarmerStats(response.length);
                        updateSearchStatus(response.length);

                        if (response.length === 0) {
                            renderEmptyState();
                        } else {
                            renderFarmerTable(response);
                        }
                    }
                } catch (e) {
                    console.error('Error parsing farmer data:', e);
                    console.log('Raw content that failed to parse:', farmerList.textContent);
                    return;
                }
            });

            // Add search-specific HTMX handlers
            htmx.on('#search-input', 'htmx:beforeRequest', function(evt) {
                console.log('Search request starting');
                const searchQuery = document.getElementById('search-input').value.trim();
                isSearching = searchQuery.length > 0;
                currentSearchQuery = searchQuery;

                // Reset to page 1 when starting a new search
                if (currentSearchQuery !== searchQuery) {
                    currentPage = 1;
                    document.querySelector('[name="page"]').value = '1';
                }

                showSearchStatus(true);
            });

            htmx.on('#search-input', 'htmx:afterRequest', function(evt) {
                console.log('Search request completed');
                showSearchStatus(false);
            });

            // Add HTMX error handling
            htmx.on('htmx:responseError', function(evt) {
                console.error('HTMX Response Error:', evt.detail);
                showNotification('Error loading data: ' + evt.detail.xhr.status, 'error');
                showSearchStatus(false);
            });

            htmx.on('htmx:sendError', function(evt) {
                console.error('HTMX Send Error:', evt.detail);
                showNotification('Network error occurred', 'error');
                showSearchStatus(false);
            });

            function updateFarmerStats(total, currentPageCount) {
                const statsElement = document.getElementById('farmer-stats');
                if (statsElement) {
                    if (totalPages > 1) {
                        const startItem = (currentPage - 1) * pageSize + 1;
                        const endItem = Math.min(currentPage * pageSize, total);
                        statsElement.innerHTML = `
                            <div>Showing ${startItem}-${endItem} of ${total} farmer${total !== 1 ? 's' : ''}</div>
                            <div class="text-xs text-gray-400">Page ${currentPage} of ${totalPages}</div>
                        `;
                    } else {
                        statsElement.textContent = `Total: ${total} farmer${total !== 1 ? 's' : ''}`;
                    }
                }
            }

            function updateSearchStatus(count) {
                const searchQuery = document.getElementById('search-input').value.trim();
                const statusElement = document.getElementById('search-status');
                const countElement = document.getElementById('search-count');

                if (searchQuery && statusElement && countElement) {
                    countElement.textContent = count;
                    statusElement.classList.remove('hidden');
                } else if (statusElement) {
                    statusElement.classList.add('hidden');
                }
            }

            function showSearchStatus(isSearching) {
                const loadingElement = document.getElementById('search-loading');
                const statusElement = document.getElementById('search-status');

                if (isSearching) {
                    if (statusElement) statusElement.classList.add('hidden');
                } else {
                    // Status will be updated by updateSearchStatus
                }
            }

            function renderEmptyState() {
                const farmerList = document.getElementById('farmer-list');
                const searchQuery = document.getElementById('search-input').value.toLowerCase();
                const isSearching = searchQuery.length > 0;

                farmerList.innerHTML = `
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                                ${isSearching ? `<p class="text-sm text-gray-500 mt-1">Search results for: "<span class="font-medium text-gray-700">${searchQuery}</span>"</p>` : ''}
                            </div>
                            <div class="text-right">
                                <div id="farmer-stats" class="text-sm text-gray-500">Total: 0 farmers</div>
                                ${isSearching ? `<button onclick="clearSearch()" class="text-xs text-blue-600 hover:text-blue-800 mt-1">Clear search</button>` : ''}
                            </div>
                        </div>
                        <div class="text-center py-16">
                            ${isSearching ? `
                                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <h3 class="mt-4 text-lg font-medium text-gray-900">No farmers match your search</h3>
                                <p class="mt-2 text-sm text-gray-500">
                                    We couldn't find any farmers matching "<span class="font-medium">${searchQuery}</span>".
                                    <br>Try adjusting your search terms or browse all farmers.
                                </p>
                                <div class="mt-6">
                                    <button onclick="clearSearch()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        Clear search
                                    </button>
                                </div>
                            ` : `
                                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                <h3 class="mt-4 text-lg font-medium text-gray-900">No farmers found</h3>
                                <p class="mt-2 text-sm text-gray-500">
                                    This cooperative doesn't have any farmers yet.
                                    <br>Add farmers to get started.
                                </p>
                                <div class="mt-6">
                                    <button onclick="showAddFarmerModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Add first farmer
                                    </button>
                                </div>
                            `}
                        </div>
                    </div>
                `;
            }

            function renderFarmerTable(farmers) {
                const farmerList = document.getElementById('farmer-list');
                const searchQuery = document.getElementById('search-input').value.toLowerCase();

                let tableHTML = `
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                                ${searchQuery ? `<p class="text-sm text-gray-500 mt-1">Search results for: "<span class="font-medium text-gray-700">${searchQuery}</span>"</p>` : ''}
                            </div>
                            <div class="text-right">
                                <div id="farmer-stats" class="text-sm text-gray-500">Total: ${farmers.length} farmer${farmers.length !== 1 ? 's' : ''}</div>
                                ${searchQuery ? `<button onclick="clearSearch()" class="text-xs text-blue-600 hover:text-blue-800 mt-1">Clear search</button>` : ''}
                            </div>
                        </div>
                        <div class="overflow-x-auto shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <span>Farmer Details</span>
                                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <span>Contact Info</span>
                                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <span>Additional Info</span>
                                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <span>Actions</span>
                                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                                </svg>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                `;

                farmers.forEach((farmer, index) => {
                    const rowClass = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';

                    // Highlight search terms
                    const highlightedName = highlightSearchTerm(farmer.name || 'N/A', searchQuery);
                    const highlightedSno = highlightSearchTerm(farmer.sno || 'N/A', searchQuery);
                    const highlightedPhone = highlightSearchTerm(farmer.mobile_number || 'N/A', searchQuery);

                    // Format additional info
                    const location = farmer.location || 'Not specified';
                    const gender = farmer.gender || 'Not specified';
                    const bank = farmer.bank || 'Not specified';
                    const accountNumber = farmer.account_number || 'Not specified';

                    tableHTML += `
                        <tr class="${rowClass} hover:bg-blue-50 transition-colors duration-150 ease-in-out">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                            <span class="text-sm font-medium text-white">${getInitials(farmer.name)}</span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">${highlightedName}</div>
                                        <div class="text-sm text-gray-500">SNO: ${highlightedSno}</div>
                                        <div class="text-xs text-gray-400">ID: ${farmer.id}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                        </svg>
                                        <span>${highlightedPhone}</span>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span>${location}</span>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="space-y-2">
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getGenderBadgeClass(farmer.gender)}">
                                            ${gender}
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                            </svg>
                                            <span>${bank}</span>
                                        </div>
                                        ${accountNumber !== 'Not specified' ? `<div class="mt-1">Acc: ${accountNumber}</div>` : ''}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <button onclick="editFarmer(${farmer.id})"
                                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </button>
                                    <button onclick="deleteFarmer(${farmer.id})"
                                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });

                tableHTML += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;

                farmerList.innerHTML = tableHTML;
            }

            function renderPagination(paginationData) {
                const farmerList = document.getElementById('farmer-list');

                if (paginationData.total_pages <= 1) {
                    return; // No pagination needed
                }

                const paginationHTML = `
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <!-- Mobile pagination -->
                            <button onclick="goToPage(${paginationData.page - 1})"
                                    ${!paginationData.has_prev ? 'disabled' : ''}
                                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${!paginationData.has_prev ? 'opacity-50 cursor-not-allowed' : ''}">
                                Previous
                            </button>
                            <button onclick="goToPage(${paginationData.page + 1})"
                                    ${!paginationData.has_next ? 'disabled' : ''}
                                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${!paginationData.has_next ? 'opacity-50 cursor-not-allowed' : ''}">
                                Next
                            </button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium">${(paginationData.page - 1) * paginationData.page_size + 1}</span>
                                    to <span class="font-medium">${Math.min(paginationData.page * paginationData.page_size, paginationData.total)}</span>
                                    of <span class="font-medium">${paginationData.total}</span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <!-- Previous button -->
                                    <button onclick="goToPage(${paginationData.page - 1})"
                                            ${!paginationData.has_prev ? 'disabled' : ''}
                                            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${!paginationData.has_prev ? 'opacity-50 cursor-not-allowed' : ''}">
                                        <span class="sr-only">Previous</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </button>

                                    ${generatePageNumbers(paginationData)}

                                    <!-- Next button -->
                                    <button onclick="goToPage(${paginationData.page + 1})"
                                            ${!paginationData.has_next ? 'disabled' : ''}
                                            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${!paginationData.has_next ? 'opacity-50 cursor-not-allowed' : ''}">
                                        <span class="sr-only">Next</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                `;

                farmerList.insertAdjacentHTML('beforeend', paginationHTML);
            }

            function generatePageNumbers(paginationData) {
                let pageNumbers = '';
                const currentPage = paginationData.page;
                const totalPages = paginationData.total_pages;

                // Show up to 7 page numbers with ellipsis
                let startPage = Math.max(1, currentPage - 3);
                let endPage = Math.min(totalPages, currentPage + 3);

                // Adjust if we're near the beginning or end
                if (currentPage <= 4) {
                    endPage = Math.min(7, totalPages);
                }
                if (currentPage > totalPages - 4) {
                    startPage = Math.max(1, totalPages - 6);
                }

                // First page
                if (startPage > 1) {
                    pageNumbers += `
                        <button onclick="goToPage(1)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            1
                        </button>
                    `;
                    if (startPage > 2) {
                        pageNumbers += `
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                ...
                            </span>
                        `;
                    }
                }

                // Page numbers
                for (let i = startPage; i <= endPage; i++) {
                    const isCurrentPage = i === currentPage;
                    pageNumbers += `
                        <button onclick="goToPage(${i})"
                                class="relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                    isCurrentPage
                                        ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                        : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                }">
                            ${i}
                        </button>
                    `;
                }

                // Last page
                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        pageNumbers += `
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                ...
                            </span>
                        `;
                    }
                    pageNumbers += `
                        <button onclick="goToPage(${totalPages})" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            ${totalPages}
                        </button>
                    `;
                }

                return pageNumbers;
            }

            function goToPage(page) {
                if (page < 1 || page > totalPages || page === currentPage) {
                    return;
                }

                currentPage = page;
                document.querySelector('[name="page"]').value = page;

                // Reset search to page 1 when searching
                if (isSearching) {
                    htmx.trigger('#search-input', 'keyup');
                } else {
                    htmx.trigger('#cooperative-select', 'change');
                }
            }

            function getGenderBadgeClass(gender) {
                switch (gender?.toLowerCase()) {
                    case 'male': return 'bg-blue-100 text-blue-800';
                    case 'female': return 'bg-pink-100 text-pink-800';
                    default: return 'bg-gray-100 text-gray-800';
                }
            }

            function highlightSearchTerm(text, searchTerm) {
                if (!searchTerm || searchTerm.length < 2) return text;

                const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
                return text.replace(regex, '<mark class="bg-yellow-200 text-yellow-900 px-1 rounded">$1</mark>');
            }

            function escapeRegExp(string) {
                return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            }

            function getInitials(name) {
                if (!name) return '?';
                return name.split(' ')
                    .map(word => word.charAt(0))
                    .join('')
                    .toUpperCase()
                    .substring(0, 2);
            }

            function clearSearch() {
                document.getElementById('search-input').value = '';
                currentSearchQuery = '';
                isSearching = false;
                currentPage = 1;
                document.querySelector('[name="page"]').value = '1';

                if (currentCooperativeId) {
                    htmx.trigger('#cooperative-select', 'change');
                }
            }

            function editFarmer(farmerId) {
                showNotification(`Edit farmer ${farmerId} - Coming soon`, 'info');
            }

            function deleteFarmer(farmerId) {
                if (confirm('Are you sure you want to delete this farmer?')) {
                    showNotification(`Delete farmer ${farmerId} - Coming soon`, 'info');
                }
            }
        </script>
    }
}
