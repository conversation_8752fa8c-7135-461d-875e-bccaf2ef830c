package main

import (
	"context"
	"log"
	"net/http"

	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/internal/erpnext"
	"github.com/itunza/africascongress/internal/handlers"
	"github.com/itunza/africascongress/internal/middleware"
	"github.com/jackc/pgx/v5"
	"github.com/joho/godotenv"
	_ "github.com/mattn/go-sqlite3"
)

func main() {

	err := godotenv.Load(".env")
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	ctx := context.Background()
	// Initialize the database context
	dbpool := database.GetDBContext(ctx)
	defer dbpool.Close()

	dbConn, err := pgx.Connect(ctx, database.GetConnectionString())
	if err != nil {
		log.Fatalf("Unable to connect to database: %v\n", err)
	}
	defer dbConn.Close(ctx)

	queries := database.New(dbpool)

	authHandler := handlers.NewAuthHandler(queries)
	ratesHandler := handlers.NewRatesHandler(queries)

	erpnextClient := erpnext.NewClient()

	// Initialize the database schema
	err = database.InitDB(ctx, dbpool)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v\n", err)
	}

	mux := http.NewServeMux()

	// Apply logging middleware
	loggedMux := middleware.LoggingMiddleware(mux)

	mux.Handle("/favicon.ico", http.NotFoundHandler())

	// Public pages (no authentication required)
	mux.HandleFunc("/login", authHandler.LoginPage)
	mux.HandleFunc("/register", authHandler.RegisterPage)

	// Protected web pages (authentication required)
	mux.HandleFunc("/logout", middleware.PageAuthMiddleware(authHandler.HandleLogout))
	mux.HandleFunc("/", middleware.PageAuthMiddleware(handlers.HomeHandler))
	mux.HandleFunc("/cooperatives", middleware.PageAuthMiddleware(handlers.CooperativesHandler))
	mux.HandleFunc("/cooperatives/create-form", middleware.PageAuthMiddleware(handlers.CooperativeCreateHandler))
	mux.HandleFunc("/agents", middleware.PageAuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient, dbConn).AgentsHandler))
	mux.HandleFunc("/farmers", middleware.PageAuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient, dbConn).FarmersHandler))
	mux.HandleFunc("/rates", middleware.PageAuthMiddleware(ratesHandler.RatesPage))
	mux.HandleFunc("/sync", middleware.PageAuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient, dbConn).SyncHandler))

	// Protected API endpoints for forms (authentication required)
	mux.HandleFunc("/api/cooperatives/add-agent-form", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient, dbConn).RenderAddAgentForm))

	// Improved sync endpoints with progress tracking
	improvedSyncHandler := handlers.NewImprovedSyncHandler(queries, erpnextClient)
	mux.HandleFunc("/api/sync/farmers-async", middleware.AuthMiddleware(improvedSyncHandler.SyncFarmersAsync))
	mux.HandleFunc("/api/sync/farmers-progress", middleware.AuthMiddleware(improvedSyncHandler.SyncFarmersWithProgress))
	mux.HandleFunc("/api/sync/progress", middleware.AuthMiddleware(improvedSyncHandler.GetSyncProgress))
	mux.HandleFunc("/api/sync/progress-component", middleware.AuthMiddleware(improvedSyncHandler.RenderSyncProgress))
	mux.HandleFunc("/api/sync/status", middleware.AuthMiddleware(improvedSyncHandler.GetSyncStatus))
	mux.HandleFunc("/api/sync/cancel", middleware.AuthMiddleware(improvedSyncHandler.CancelSync))
	apiCooperativeHandler := handlers.NewCooperativeHandler(queries, erpnextClient, dbConn)

	// API endpoints
	mux.HandleFunc("/api/login", authHandler.Login)
	mux.HandleFunc("/api/register", authHandler.Register)

	mux.HandleFunc("/api/cooperatives", middleware.AuthMiddleware(apiCooperativeHandler.List))
	mux.HandleFunc("/api/cooperatives/create", middleware.AuthMiddleware(apiCooperativeHandler.Create))
	mux.HandleFunc("/api/cooperatives/get", middleware.AuthMiddleware(apiCooperativeHandler.Get))
	mux.HandleFunc("/api/cooperatives/update", middleware.AuthMiddleware(apiCooperativeHandler.Update))
	mux.HandleFunc("/api/cooperatives/delete", middleware.AuthMiddleware(apiCooperativeHandler.Delete))
	mux.HandleFunc("/api/cooperatives/add-farmer", middleware.AuthMiddleware(apiCooperativeHandler.AddFarmer))
	mux.HandleFunc("/api/cooperatives/list-farmers", middleware.AuthMiddleware(apiCooperativeHandler.ListFarmers))
	mux.HandleFunc("/api/cooperatives/add-agent", middleware.AuthMiddleware(apiCooperativeHandler.AddAgent))
	mux.HandleFunc("/api/cooperatives/list-agents", middleware.AuthMiddleware(apiCooperativeHandler.ListAgents))
	mux.HandleFunc("/api/cooperatives/update-farmer", middleware.AuthMiddleware(apiCooperativeHandler.UpdateFarmer))
	mux.HandleFunc("/api/cooperatives/delete-farmer", middleware.AuthMiddleware(apiCooperativeHandler.DeleteFarmer))
	mux.HandleFunc("/api/cooperatives/bulk-upload-farmers", middleware.AuthMiddleware(apiCooperativeHandler.BulkUploadFarmers))
	mux.HandleFunc("/api/cooperatives/bulk-upload-agents", middleware.AuthMiddleware(apiCooperativeHandler.BulkUploadAgents))
	mux.HandleFunc("/api/webhook", middleware.AuthMiddleware(apiCooperativeHandler.WebhookUpdate))
	mux.HandleFunc("/api/cooperatives/search-farmers", middleware.AuthMiddleware(apiCooperativeHandler.SearchFarmers))
	mux.HandleFunc("/api/cooperatives/edit-farmer", middleware.AuthMiddleware(apiCooperativeHandler.EditFarmer))
	mux.HandleFunc("/api/cooperatives/sync-agents", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient, dbConn).SyncAgents))
	mux.HandleFunc("/api/cooperatives/sync-farmers", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient, dbConn).SyncFarmers))

	// Rates API endpoints
	mux.HandleFunc("/api/rates/list", middleware.AuthMiddleware(ratesHandler.ListRates))
	mux.HandleFunc("/api/rates/agents", middleware.AuthMiddleware(ratesHandler.GetAgentsForCooperative))
	mux.HandleFunc("/api/rates/create", middleware.AuthMiddleware(ratesHandler.CreateRate))
	mux.HandleFunc("/api/rates/edit", middleware.AuthMiddleware(ratesHandler.EditRate))
	mux.HandleFunc("/api/rates/update", middleware.AuthMiddleware(ratesHandler.UpdateRate))
	mux.HandleFunc("/api/rates/cancel-edit", middleware.AuthMiddleware(ratesHandler.CancelEdit))
	mux.HandleFunc("/api/rates/delete", middleware.AuthMiddleware(ratesHandler.DeleteRate))

	// purchase order webhook
	mux.HandleFunc("/api/po-webhook", handlers.NewPurchaseOrderHandler(queries).NewPOWebhookHandler)

	//
	// mux.HandleFunc("/publish", handlers.HandlePublish(channel))
	// mux.HandleFunc("/queue-count", handlers.HandleQueueMessageCount(channel))

	// http.HandleFunc("/api/cooperatives", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).List))
	// mux.HandleFunc("/api/cooperatives/create", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).Create))
	// http.HandleFunc("/api/cooperatives/get", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).Get))
	// http.HandleFunc("/api/cooperatives/update", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).Update))
	// http.HandleFunc("/api/cooperatives/delete", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).Delete))
	// http.HandleFunc("/api/cooperatives/add-farmer", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).AddFarmer))
	// mux.HandleFunc("/api/cooperatives/list-farmers", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).ListFarmers))
	// http.HandleFunc("/api/cooperatives/add-agent", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).AddAgent))
	// http.HandleFunc("/api/cooperatives/list-agents", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).ListAgents))
	// http.HandleFunc("/api/cooperatives/update-farmer", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).UpdateFarmer))
	// http.HandleFunc("/api/cooperatives/delete-farmer", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).DeleteFarmer))
	// http.HandleFunc("/api/cooperatives/bulk-upload-farmers", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).BulkUploadFarmers))
	// http.HandleFunc("/api/cooperatives/bulk-upload-agents", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).BulkUploadAgents))
	// http.HandleFunc("/api/webhook", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).WebhookUpdate))
	// http.HandleFunc("/api/cooperatives/search-farmers", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).SearchFarmers))
	// http.HandleFunc("/api/cooperatives/edit-farmer", middleware.AuthMiddleware(handlers.NewCooperativeHandler(queries, erpnextClient).EditFarmer))

	// http.HandleFunc("/", handlers.HomeHandler)
	// http.HandleFunc("/cooperatives", handlers.CooperativesHandler)
	// http.HandleFunc("/farmers", handlers.FarmersHandler)
	// http.HandleFunc("/agents", handlers.NewCooperativeHandler(queries, erpnextClient).AgentsHandler)
	// // http.HandleFunc("/api/cooperatives/add-agent-form", cooperativeHandler.RenderAddAgentForm)
	// http.HandleFunc("/api/cooperatives/add-agent-form", handlers.NewCooperativeHandler(queries, erpnextClient).RenderAddAgentForm)

	// Start the consumer in a goroutine

	log.Println("Starting server on :8998")
	log.Fatal(http.ListenAndServe(":8998", loggedMux))
}

// // Subscriber function for multiple queues
// func consumeMessages(channel *amqp.Channel, queueName string) {
// 	msgs, err := channel.Consume(
// 		queueName, // queue
// 		"",        // consumer
// 		true,      // auto-ack
// 		false,     // exclusive
// 		false,     // no-local
// 		false,     // no-wait
// 		nil,       // args
// 	)
// 	if err != nil {
// 		log.Fatalf("Failed to register a consumer for queue %s: %v", queueName, err)
// 	}

// 	go func() {
// 		for msg := range msgs {
// 			log.Printf("Received a message from queue %s: %s", queueName, msg.Body)
// 			// Process the message as needed
// 		}
// 	}()
// }
